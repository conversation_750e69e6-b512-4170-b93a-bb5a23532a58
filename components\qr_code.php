<?php
// Include configuration file if not already included
if (!defined('BASE_URL')) {
    require_once __DIR__ . '/../includes/config.php';
}
?>
<center>
<!-- Bootstrap Modal for QR Code Display -->
<div class="modal fade" id="qrModal" tabindex="-1" aria-labelledby="qrModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content rounded-0 p-2">
            <div class="modal-header d-flex justify-content-center">
                <h5 class="modal-title" id="qrModalLabel">Your Account QR Code</h5>
            </div>
            <div class="modal-body py-2" style="height: 222px !important;">
                <div id="qrcode" class="text-center w-100"></div>
            </div>
            <div class="modal-footer py-2 d-flex justify-content-center">
                <!-- Add User with QR Code Button -->
                <button type="button" class="btn btn-default mx-2" data-bs-toggle="modal" data-bs-target="#qrScannerModal">
                    <img src="<?php echo url('assets/img/QRscan.png'); ?>" alt="Add Friend with QR Code" style="width: 50px;">
                </button>
                <!-- Download Your QR Code Button -->
                <a id="downloadLink" download="<?php echo $username; ?>_C-Health_QR-code.png" class="btn btn-default mx-2">
                    <img src="<?php echo url('assets/img/QRdownload.png'); ?>" alt="Download Your QR-Code" style="width: 50px;">
                </a>
            </div><hr class="text-body-tertiary">
            <div class="d-flex justify-content-center mt-3 mb-1">
                <button type="button" class="btn btn-secondary rounded-pill p-1 px-2 mx-2 w-25" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap Modal for QR Code Scanner -->
<div class="modal fade" id="qrScannerModal" tabindex="-1" aria-labelledby="qrScannerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header d-flex justify-content-center">
                <h5 class="modal-title" id="qrScannerModalLabel">QR Code Scanner</h5>
            </div>
            <div class="modal-body">
                <div id="reader" class="text-center" style="width: 75%;"></div>
                <div id="scanResult" class="text-center my-2"></div>
            </div>
            <div class="modal-footer d-flex justify-content-center">
                <button type="button" class="btn btn-secondary rounded-pill p-1 px-2 mx-2 my-3" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
</center>
<script>
    // Function to generate and display the QR code
    function showQRCode(accountNumber) {
        var qr = qrcode(0, 'L'); // 0 means automatic version
        qr.addData(accountNumber);
        qr.make();
        var qrImgTag = qr.createImgTag(6); // 6 is the scaling factor for a larger QR code
        document.getElementById('qrcode').innerHTML = qrImgTag;
        document.getElementById('downloadLink').href = qr.createDataURL();
    }

    // Initialize QR Code Modal
    $(document).ready(function() {
        $('#qrModal').on('show.bs.modal', function () {
            var accountNumber = '<?php echo $account_number; ?>';
            showQRCode(accountNumber);
        });
    });
</script>

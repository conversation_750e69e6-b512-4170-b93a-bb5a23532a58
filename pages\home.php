<div class="px-3 position-fixed">
    <p class="d-inline h4">Date:</p> <p class="d-inline mx-1 h4" id="dateDisplay"></p>
    <p class="d-inline mx-1 h4" id="dayDisplay"></p>
    <br>
    <p class="d-inline h4">Time:</p> <p class="d-inline mx-1 h4" id="timeDisplay"></p>
    <p class="d-inline mx-1 h4" id="ampmDisplay"></p>
</div>

<script>
var currentAMPM = ''; // Global variable to store the current AM/PM value
var currentDateDisplayed = ''; // Global variable to store the current date displayed

// Function to update date and time
function updateDateTime() {
    var currentDate = new Date();
    var year = currentDate.getFullYear();
    var month = ('0' + (currentDate.getMonth() + 1)).slice(-2); // Adding leading zero if needed
    var day = ('0' + currentDate.getDate()).slice(-2); // Adding leading zero if needed

    var date = year + '/' + month + '/' + day;
    var dayOfWeek = currentDate.toLocaleDateString('en-US', { weekday: 'long' }); // Get the full name of the day of the week

    // Update date display if date changes
    if (date !== currentDateDisplayed) {
        document.getElementById('dateDisplay').innerHTML = date;
        document.getElementById('dayDisplay').innerHTML = dayOfWeek;
        currentDateDisplayed = date;
    }
}

// Function to update time
function updateTime() {
    var currentTime = new Date();
    var time = currentTime.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit', hour12: true}).replace(/ (AM|PM)/, ''); // Format time and remove AM/PM
    var ampm = currentTime.toLocaleTimeString([], {hour12: true}).split(' ')[1]; // Get AM or PM

    // Update time display if AM/PM changes
    if (ampm !== currentAMPM) {
        document.getElementById('ampmDisplay').innerHTML = ampm;
        currentAMPM = ampm;
    }

    // Update time display every second
    document.getElementById('timeDisplay').innerHTML = time;
}

// Update date and time every 24 hours
setInterval(updateDateTime, 1000);

// Update time every second
setInterval(updateTime, 1000);

// Initial update
updateDateTime();
updateTime();
</script>

<?php
// Get user_id from session
$user_id = $_SESSION['user_id'];

// Get today's date in the format 'Y-m-d'
$today_date = date('Y-m-d');

// Retrieve health_status for today from user_nutrition
$sql = "SELECT health_status FROM user_nutrition WHERE user_id = ? AND input_date = ?";
$stmt = mysqli_prepare($con, $sql);

if (!$stmt) {
    die("Error: " . mysqli_error($con));
}

mysqli_stmt_bind_param($stmt, "is", $user_id, $today_date);
mysqli_stmt_execute($stmt);

$result = mysqli_stmt_get_result($stmt);
$row = mysqli_fetch_assoc($result);

$health_status_today = $row['health_status'] ?? null;

mysqli_stmt_close($stmt);
?>

<?php
// Retrieve water consumption for today
$sql_water = "
    SELECT SUM(quantity) AS total_water
    FROM food_consumption
    JOIN nutrition ON food_consumption.food_id = nutrition.id
    WHERE nutrition.nutrition = 'Water' 
    AND food_consumption.date_consumed = ?
    AND food_consumption.user_id = ?
";
$stmt_water = mysqli_prepare($con, $sql_water);

if (!$stmt_water) {
    die("Error: " . mysqli_error($con));
}

mysqli_stmt_bind_param($stmt_water, "si", $today_date, $user_id);
mysqli_stmt_execute($stmt_water);
$result_water = mysqli_stmt_get_result($stmt_water);

$water_data = mysqli_fetch_assoc($result_water);
$total_water = $water_data['total_water'] ? $water_data['total_water'] : 0;

mysqli_stmt_close($stmt_water);

// Calculate the fill percentage for water consumption
$max_glasses = 8;
$fill_percentage = round(min(($total_water / $max_glasses) * 100, 100)); // Cap at 100%
$hydration_message = $total_water > 0 ? "<p style='color: #FFEB3B;' class='display-6'>$fill_percentage%</p><p class='text-light h6'>hydrated</p>" : "<p class='text-light h6'>Drink water to stay hydrated</p>";

?>

<?php
// Retrieve exercise data for today
$sql_exercise = "
    SELECT SUM(quantity) AS total_minutes
    FROM food_consumption
    JOIN nutrition ON food_consumption.food_id = nutrition.id
    WHERE nutrition.nutrition IN ('Walking', 'Running', 'Swimming')
    AND food_consumption.date_consumed = ?
    AND food_consumption.user_id = ?
";
$stmt_exercise = mysqli_prepare($con, $sql_exercise);
mysqli_stmt_bind_param($stmt_exercise, "si", $today_date, $user_id);
mysqli_stmt_execute($stmt_exercise);
$result_exercise = mysqli_stmt_get_result($stmt_exercise);
$row_exercise = mysqli_fetch_assoc($result_exercise);
$total_exercise = $row_exercise['total_minutes'] ? $row_exercise['total_minutes'] : 0;
mysqli_stmt_close($stmt_exercise);

// Calculate the fill percentage for exercise
$max_minutes = 30;
$exercise_percentage = round(min(($total_exercise / $max_minutes) * 100, 100));
$exercise_message = $total_exercise > 0 ? "<p style='color: #FFEB3B;' class='display-6'>$exercise_percentage%</p><p class='text-light h6'>exercised</p>" : "<p class='text-light h6'>Start exercising to stay healthy</p>";
?>

<?php
// Get user's age based on birthdate from the database
$sql_age = "SELECT birth_date FROM users WHERE user_id = ?";
$stmt_age = mysqli_prepare($con, $sql_age);
mysqli_stmt_bind_param($stmt_age, "i", $user_id);
mysqli_stmt_execute($stmt_age);
$result_age = mysqli_stmt_get_result($stmt_age);
$row_age = mysqli_fetch_assoc($result_age);

$user_birthdate = $row_age['birth_date'];
$age = date_diff(date_create($user_birthdate), date_create('today'))->y;

mysqli_stmt_close($stmt_age);

// Get the user's medical condition(s) from the database
$sql_medical = "SELECT medical_condition FROM users WHERE user_id = ?";
$stmt_medical = mysqli_prepare($con, $sql_medical);
mysqli_stmt_bind_param($stmt_medical, "i", $user_id);
mysqli_stmt_execute($stmt_medical);
$result_medical = mysqli_stmt_get_result($stmt_medical);
$row_medical = mysqli_fetch_assoc($result_medical);

$medical_conditions = explode(', ', $row_medical['medical_condition']); // Split multiple conditions

// Determine sleep hours needed based on age and medical conditions
if (in_array('Pregnancy', $medical_conditions)) {
    $needed_sleep = 8; // For pregnancy, suggest 8 hours of sleep
} elseif ($age >= 18) {
    $needed_sleep = 7;
} elseif ($age >= 13) {
    $needed_sleep = 8;
} elseif ($age >= 6) {
    $needed_sleep = 9;
} elseif ($age >= 3) {
    $needed_sleep = 10;
} elseif ($age >= 1) {
    $needed_sleep = 11;
} else {
    $needed_sleep = 12;
}

// Retrieve sleep data for today
$sql_sleep = "
    SELECT SUM(quantity) AS total_sleep
    FROM food_consumption
    JOIN nutrition ON food_consumption.food_id = nutrition.id
    WHERE nutrition.nutrition = 'sleep'
    AND food_consumption.date_consumed = ?
    AND food_consumption.user_id = ?
";
$stmt_sleep = mysqli_prepare($con, $sql_sleep);
mysqli_stmt_bind_param($stmt_sleep, "si", $today_date, $user_id);
mysqli_stmt_execute($stmt_sleep);
$result_sleep = mysqli_stmt_get_result($stmt_sleep);
$row_sleep = mysqli_fetch_assoc($result_sleep);
$total_sleep = $row_sleep['total_sleep'] ? $row_sleep['total_sleep'] : 0;
mysqli_stmt_close($stmt_sleep);

// Calculate the sleep percentage
$sleep_percentage = round(min(($total_sleep / $needed_sleep) * 100, 100));
$sleep_message = $total_sleep > 0 ? "<p style='color: #FFEB3B;' class='h5'>$sleep_percentage%</p><p class='text-light'>slept</p>" : "<p class='text-light h6'>Sleep<br> at least<br> $needed_sleep <span>hours </span></p>";
?>

<?php
if ($health_status_today !== null) {
    echo "<div class='centered h-50 mt-5 pt-4 text-center'>
            <div class='healthCircle centered' style='width:200px; height: 200px; border-radius: 50%; background-color: #2E7D32;'>
                <div>
                    <p class='text-light h5'>You  are</p>
                    <p style='color: #FFEB3B;' class='display-1'><b>$health_status_today%</b></p>
                    <p class='text-light h5'>healthy today!</p>
                </div>
            </div>
        </div>";
} else {
    echo "<div class='centered h-50 mt-5 pb-4 text-center'>
            <div class='centered rounded-circle p-3' style='width: 200px; height: 200px; background-color: #E0E0E0;'>
                <div>
                    <p class='text-muted text-wrap h5'>No health status data available for today, Go to input</p>
                </div>
            </div>
        </div>";
}

echo "<div style='height: 30vh; position: relative; margin-top: -50px !important;'>";

echo "<div class='position-absolute top-0 start-0 ms-3 text-center'>
        <div class='waterCircle centered' style='width: 140px; height: 140px; position: relative; background-color: #2E7D32; border-radius: 50%; overflow: hidden;'>
            <svg viewBox='0 0 36 36' class='circular-chart' style='position: absolute; top: 0; left: 0; width: 100%; height: 100%;'>
                <path class='circle-background' stroke='#E0E0E0' stroke-width='2' fill='none' d='M18 2.0845A15.9155 15.9155 0 0 1 33.9155 18A15.9155 15.9155 0 0 1 18 33.9155A15.9155 15.9155 0 0 1 2.0845 18A15.9155 15.9155 0 0 1 18 2.0845Z'></path>
                <path class='circle-fill' stroke='#2196F3' stroke-width='1.5' stroke-dasharray='$fill_percentage, 100' fill='none' d='M18 2.0845A15.9155 15.9155 0 0 1 33.9155 18A15.9155 15.9155 0 0 1 18 33.9155A15.9155 15.9155 0 0 1 2.0845 18A15.9155 15.9155 0 0 1 18 2.0845Z'></path>
            </svg>
            <div class='text-light h6' style='position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);'>
                <p>$hydration_message</p>
            </div>
            <!-- Water Bubbles Animation -->
            <div class='bubbles-container'>
                <span class='bubble'></span>
                <span class='bubble'></span>
                <span class='bubble'></span>
                <span class='bubble'></span>
                <span class='bubble'></span>
            </div>
        </div>
    </div>";


    echo "<div class='position-absolute top-0 end-0 me-3 text-center'>
    <div class='exerciseCircle centered' style='width: 140px; height: 140px; position: relative; background-color: #2E7D32; border-radius: 50%; overflow: hidden;'>
        <svg viewBox='0 0 36 36' class='circular-chart' style='position: absolute; top: 0; left: 0; width: 100%; height: 100%;'>
            <path class='circle-background' stroke='#E0E0E0' stroke-width='2' fill='none' d='M18 2.0845A15.9155 15.9155 0 0 1 33.9155 18A15.9155 15.9155 0 0 1 18 33.9155A15.9155 15.9155 0 0 1 2.0845 18A15.9155 15.9155 0 0 1 18 2.0845Z'></path>
            <path class='circle-fill' stroke='#FF9800' stroke-width='1.5' stroke-dasharray='$exercise_percentage, 100' fill='none' d='M18 2.0845A15.9155 15.9155 0 0 1 33.9155 18A15.9155 15.9155 0 0 1 18 33.9155A15.9155 15.9155 0 0 1 2.0845 18A15.9155 15.9155 0 0 1 18 2.0845Z'></path>
        </svg>
        <div class='text-light h6' style='position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);'>
            <p>$exercise_message</p>
        </div>
        <!-- Subtle Moving Pulsing Dots at Bottom -->
        <div class='pulsing-dots-container'>
            <span class='pulse-dot'></span>
            <span class='pulse-dot'></span>
            <span class='pulse-dot'></span>
        </div>
    </div>
</div>";








echo "<div class='centered text-center' style='padding-top: 8rem !important;'>
        <div class='sleepCircle centered' style='font-size: 14px; width: 100px; height: 100px; position: relative; background-color: #2E7D32; border-radius: 50%; overflow: hidden;'>
            <svg viewBox='0 0 36 36' class='circular-chart' style='position: absolute; top: 0; left: 0; width: 100%; height: 100%;'>
                <path class='circle-background' stroke='#E0E0E0' stroke-width='2' fill='none' d='M18 2.0845A15.9155 15.9155 0 0 1 33.9155 18A15.9155 15.9155 0 0 1 18 33.9155A15.9155 15.9155 0 0 1 2.0845 18A15.9155 15.9155 0 0 1 18 2.0845Z'></path>
                <path class='circle-fill' stroke='#673AB7' stroke-width='1.5' stroke-dasharray='$sleep_percentage, 100' fill='none' d='M18 2.0845A15.9155 15.9155 0 0 1 33.9155 18A15.9155 15.9155 0 0 1 18 33.9155A15.9155 15.9155 0 0 1 2.0845 18A15.9155 15.9155 0 0 1 18 2.0845Z'></path>
            </svg>
            <div class='text-light'>
                <p>$sleep_message</p>
            </div>
            <!-- Zzz Animation -->
            <div class='zzz-container'>
                <span class='zzz'>Zzz</span>
                <span class='zzz'>Zzz</span>
                <span class='zzz'>Zzz</span>
            </div>
        </div>
    </div>";



echo "</div>";
?>
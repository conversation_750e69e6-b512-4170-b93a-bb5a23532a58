<?php
// Only start a session if one doesn't already exist
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
require_once 'includes/config.php';
require_once 'includes/db.php';

// Delete token from database
if (isset($_<PERSON><PERSON>IE['remember_me'])) {
    list($selector, $token) = explode(':', $_COOKIE['remember_me']);

    $deleteQuery = "DELETE FROM user_tokens WHERE selector = ?";
    $stmt = $con->prepare($deleteQuery);
    $stmt->bind_param("s", $selector);
    $stmt->execute();

    // Clear the cookie
    setcookie('remember_me', '', time() - 3600, '/');
}

// Destroy the session
session_unset();
session_destroy();

// Redirect to login page
header("Location: " . url('login.php'));
exit();
?>

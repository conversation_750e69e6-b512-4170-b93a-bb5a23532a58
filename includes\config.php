<?php
// Configuration file for application settings

// Determine if we're in development or production environment
$is_development = ($_SERVER['SERVER_NAME'] === 'localhost' || $_SERVER['SERVER_NAME'] === '127.0.0.1');

// Set the base URL based on environment
if ($is_development) {
    // Local development - include the folder name
    $base_url = '/c-health';
} else {
    // Production environment - assume it's at the root
    $base_url = '';
}

// Define constants
if (!defined('BASE_URL')) {
    define('BASE_URL', $base_url);
}

// Function to generate a URL with the correct base path
if (!function_exists('url')) {
    function url($path = '') {
        // Remove leading slash if present to avoid double slashes
        if (substr($path, 0, 1) === '/') {
            $path = substr($path, 1);
        }

        return BASE_URL . '/' . $path;
    }
}
?>
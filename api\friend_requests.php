<?php
session_start();
require_once '../includes/config.php';
require '../includes/db.php';

// Ensure the user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . url('login.php'));
    exit();
}

$current_user_id = $_SESSION['user_id'];

// Fetch incoming friend requests
$stmt = $con->prepare("
    SELECT u.user_id, u.username, u.full_name
    FROM friends f
    JOIN users u ON f.user_id = u.user_id
    WHERE f.friend_id = ? AND f.status = 'pending'
");
$stmt->bind_param("i", $current_user_id);
$stmt->execute();
$requests_result = $stmt->get_result();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Friend Requests</title>
    <!-- Include CSS and JS files -->
    <link rel="stylesheet" href="<?php echo url('assets/css/style-dynamic.php'); ?>">
</head>
<body>
    <h2>Incoming Friend Requests</h2>
    <?php if ($requests_result->num_rows > 0): ?>
        <ul>
            <?php while($request = $requests_result->fetch_assoc()): ?>
                <li>
                    <?php echo htmlspecialchars($request['full_name']); ?> (@<?php echo htmlspecialchars($request['username']); ?>)
                    <form action="<?php echo url('api/manage_friend_request.php'); ?>" method="post" style="display:inline;">
                        <input type="hidden" name="requester_id" value="<?php echo $request['user_id']; ?>">
                        <button type="submit" name="action" value="accept">Accept</button>
                        <button type="submit" name="action" value="reject">Reject</button>
                    </form>
                </li>
            <?php endwhile; ?>
        </ul>
    <?php else: ?>
        <p>No incoming friend requests.</p>
    <?php endif; ?>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>C-Health - Offline</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: #f5f5f5;
        flex-direction: column;
        text-align: center;
      }
      .container {
        max-width: 600px;
        padding: 20px;
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #2e7d32;
      }
      p {
        color: #666;
        line-height: 1.6;
      }
      .logo {
        max-width: 150px;
        margin-bottom: 20px;
      }
      .btn {
        display: inline-block;
        background-color: #2e7d32;
        color: white;
        padding: 10px 20px;
        border-radius: 50px;
        text-decoration: none;
        margin-top: 20px;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <img src="assets/img/logo.png" alt="C-Health Logo" class="logo" />
      <h1>You're Offline</h1>
      <p>
        It seems you're not connected to the internet. Please check your
        connection and try again.
      </p>
      <p>
        C-Health requires an internet connection to access your health data and
        provide you with the best experience.
      </p>
      <a href="login.php" class="btn">Try Again</a>
    </div>
    <script>
      // Check if the user comes back online
      window.addEventListener("online", function () {
        window.location.reload();
      });

      // Add event listener to the "Try Again" button
      document.querySelector(".btn").addEventListener("click", function (e) {
        e.preventDefault();
        window.location.reload();
      });
    </script>
  </body>
</html>

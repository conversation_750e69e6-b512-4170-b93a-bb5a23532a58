// sw-new.js (Service Worker)
// Version: 1.0

// Cache name - update this when you make changes to the service worker
const CACHE_NAME = "c-health-cache-v3";

// Install event handler
self.addEventListener("install", function (event) {
  console.log("Service Worker installing...");
  
  // Skip waiting to activate immediately
  self.skipWaiting();
  
  // Get the base path from the service worker scope
  const basePath = self.registration.scope.replace(/\/$/, "");
  
  // Cache essential resources
  event.waitUntil(
    caches.open(CACHE_NAME).then(function (cache) {
      console.log("Opened cache:", CACHE_NAME);
      return cache.addAll([
        basePath + "/offline.html",
        basePath + "/assets/img/logo.png",
        basePath + "/assets/img/fav.ico"
      ]);
    })
  );
});

// Activate event handler - clean up old caches
self.addEventListener("activate", function (event) {
  console.log("Service Worker activating...");
  
  event.waitUntil(
    caches.keys().then(function (cacheNames) {
      return Promise.all(
        cacheNames.map(function (cacheName) {
          if (cacheName !== CACHE_NAME) {
            console.log("Deleting old cache:", cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // Claim clients to take control immediately
  return self.clients.claim();
});

// Fetch event handler
self.addEventListener("fetch", function (event) {
  // Skip non-GET requests
  if (event.request.method !== "GET") {
    return;
  }
  
  const url = new URL(event.request.url);
  const basePath = self.registration.scope.replace(/\/$/, "");
  
  // For HTML documents, use network-first strategy
  if (event.request.mode === "navigate" || event.request.destination === "document") {
    event.respondWith(
      fetch(event.request)
        .catch(function () {
          return caches.match(basePath + "/offline.html");
        })
    );
    return;
  }
  
  // For CSS files, use network-first strategy
  if (event.request.destination === "style" || 
      url.pathname.endsWith(".css") || 
      (url.pathname.endsWith(".php") && url.pathname.includes("/css/"))) {
    event.respondWith(
      fetch(event.request)
        .then(function (response) {
          // Create a copy of the response to store in cache
          const clonedResponse = response.clone();
          
          // Store in cache
          caches.open(CACHE_NAME)
            .then(function (cache) {
              cache.put(event.request, clonedResponse);
            })
            .catch(function (err) {
              console.error("Failed to cache CSS:", err);
            });
            
          return response;
        })
        .catch(function () {
          return caches.match(event.request);
        })
    );
    return;
  }
  
  // For images, use cache-first strategy
  if (event.request.destination === "image") {
    event.respondWith(
      caches.match(event.request)
        .then(function (response) {
          return response || fetch(event.request)
            .then(function (fetchResponse) {
              // Create a copy to store in cache
              const clonedResponse = fetchResponse.clone();
              
              // Store in cache
              caches.open(CACHE_NAME)
                .then(function (cache) {
                  cache.put(event.request, clonedResponse);
                })
                .catch(function (err) {
                  console.error("Failed to cache image:", err);
                });
                
              return fetchResponse;
            })
            .catch(function () {
              // If fetch fails, return the logo as fallback
              return caches.match(basePath + "/assets/img/logo.png");
            });
        })
    );
    return;
  }
  
  // For all other requests, use stale-while-revalidate strategy
  event.respondWith(
    caches.match(event.request)
      .then(function (response) {
        // Return cached response immediately if available
        const fetchPromise = fetch(event.request)
          .then(function (networkResponse) {
            // Create a copy to store in cache
            const clonedResponse = networkResponse.clone();
            
            // Store in cache
            caches.open(CACHE_NAME)
              .then(function (cache) {
                cache.put(event.request, clonedResponse);
              })
              .catch(function (err) {
                console.error("Failed to cache resource:", err);
              });
              
            return networkResponse;
          })
          .catch(function (error) {
            console.error("Fetch failed:", error);
          });
          
        return response || fetchPromise;
      })
  );
});

<?php
if (!isset($_SESSION['username']) && isset($_COOKIE['remember_me'])) {
    list($selector, $token) = explode(':', $_COOKIE['remember_me']);

    $query = "SELECT user_tokens.token_hash, user_tokens.expires, users.* FROM user_tokens INNER JOIN users ON user_tokens.user_id = users.user_id WHERE user_tokens.selector = ?";
    $stmt = $con->prepare($query);
    $stmt->bind_param("s", $selector);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows === 1) {
        $row = $result->fetch_assoc();

        if (hash_equals($row['token_hash'], hash('sha256', $token))) {
            if (strtotime($row['expires']) > time()) {
                // Token is valid and not expired
                $_SESSION['user_id'] = $row['user_id'];
                $_SESSION['username'] = $row['username'];

                // Optionally, regenerate token to prevent fixation
            } else {
                // Token expired; delete from database
                $deleteQuery = "DELETE FROM user_tokens WHERE selector = ?";
                $stmt = $con->prepare($deleteQuery);
                $stmt->bind_param("s", $selector);
                $stmt->execute();
            }
        } else {
            // Invalid token; possible tampering
            // Delete all tokens for security
            $deleteQuery = "DELETE FROM user_tokens WHERE user_id = ?";
            $stmt = $con->prepare($deleteQuery);
            $stmt->bind_param("i", $row['user_id']);
            $stmt->execute();
        }
    }
}
?>

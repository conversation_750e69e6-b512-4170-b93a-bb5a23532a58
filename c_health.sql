-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 28, 2025 at 01:14 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `c_health`
--

-- --------------------------------------------------------

--
-- Table structure for table `food_consumption`
--

CREATE TABLE `food_consumption` (
  `consumption_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `food_id` int(11) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `date_consumed` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `food_consumption`
--

INSERT INTO `food_consumption` (`consumption_id`, `user_id`, `food_id`, `quantity`, `date_consumed`) VALUES
(1, 58, 25, 1, '2024-09-30 00:00:00'),
(2, 58, 26, 1, '2024-09-30 00:00:00'),
(3, 58, 35, 0, '2024-09-30 00:00:00'),
(4, 58, 55, 5, '2024-09-30 00:00:00'),
(5, 58, 9, 0, '2024-09-30 00:00:00'),
(6, 58, 16, 0, '2024-09-30 00:00:00'),
(7, 58, 17, 0, '2024-09-30 00:00:00'),
(8, 58, 18, 0, '2024-09-30 00:00:00'),
(9, 58, 19, 0, '2024-09-30 00:00:00'),
(10, 58, 23, 0, '2024-09-30 00:00:00'),
(11, 58, 24, 0, '2024-09-30 00:00:00'),
(12, 58, 54, 0, '2024-09-30 00:00:00'),
(13, 58, 8, 0, '2024-09-30 00:00:00'),
(14, 58, 22, 3, '2024-09-30 00:00:00'),
(15, 58, 27, 2, '2024-09-30 00:00:00'),
(16, 58, 28, 0, '2024-09-30 00:00:00'),
(17, 58, 29, 0, '2024-09-30 00:00:00'),
(18, 58, 30, 0, '2024-09-30 00:00:00'),
(19, 58, 31, 0, '2024-09-30 00:00:00'),
(20, 58, 33, 0, '2024-09-30 00:00:00'),
(21, 58, 34, 0, '2024-09-30 00:00:00'),
(22, 58, 36, 0, '2024-09-30 00:00:00'),
(23, 58, 42, 0, '2024-09-30 00:00:00'),
(24, 58, 56, 1, '2024-09-30 00:00:00'),
(25, 58, 1, 0, '2024-09-30 00:00:00'),
(26, 58, 2, 0, '2024-09-30 00:00:00'),
(27, 58, 10, 0, '2024-09-30 00:00:00'),
(28, 58, 3, 0, '2024-09-30 00:00:00'),
(29, 58, 4, 0, '2024-09-30 00:00:00'),
(30, 58, 5, 0, '2024-09-30 00:00:00'),
(31, 58, 7, 0, '2024-09-30 00:00:00'),
(32, 58, 12, 0, '2024-09-30 00:00:00'),
(33, 58, 15, 0, '2024-09-30 00:00:00'),
(34, 58, 43, 0, '2024-09-30 00:00:00'),
(35, 58, 44, 0, '2024-09-30 00:00:00'),
(36, 58, 45, 0, '2024-09-30 00:00:00'),
(37, 58, 46, 0, '2024-09-30 00:00:00'),
(38, 58, 47, 0, '2024-09-30 00:00:00'),
(39, 58, 48, 0, '2024-09-30 00:00:00'),
(40, 58, 49, 0, '2024-09-30 00:00:00'),
(41, 58, 50, 0, '2024-09-30 00:00:00'),
(42, 58, 51, 0, '2024-09-30 00:00:00'),
(43, 58, 52, 0, '2024-09-30 00:00:00'),
(44, 58, 53, 0, '2024-09-30 00:00:00'),
(45, 58, 6, 0, '2024-09-30 00:00:00'),
(46, 58, 11, 0, '2024-09-30 00:00:00'),
(47, 58, 14, 0, '2024-09-30 00:00:00'),
(48, 58, 13, 0, '2024-09-30 00:00:00'),
(49, 58, 20, 0, '2024-09-30 00:00:00'),
(50, 58, 21, 0, '2024-09-30 00:00:00'),
(51, 58, 32, 0, '2024-09-30 00:00:00'),
(52, 58, 37, 0, '2024-09-30 00:00:00'),
(53, 58, 38, 0, '2024-09-30 00:00:00'),
(54, 58, 39, 12, '2024-09-30 00:00:00'),
(55, 58, 40, 120, '2024-09-30 00:00:00'),
(56, 58, 41, 7, '2024-09-30 00:00:00'),
(57, 58, 25, 0, '2024-09-30 00:00:00'),
(58, 58, 26, 0, '2024-09-30 00:00:00'),
(59, 58, 35, 0, '2024-09-30 00:00:00'),
(60, 58, 55, 0, '2024-09-30 00:00:00'),
(61, 58, 9, 0, '2024-09-30 00:00:00'),
(62, 58, 16, 0, '2024-09-30 00:00:00'),
(63, 58, 17, 0, '2024-09-30 00:00:00'),
(64, 58, 18, 0, '2024-09-30 00:00:00'),
(65, 58, 19, 0, '2024-09-30 00:00:00'),
(66, 58, 23, 0, '2024-09-30 00:00:00'),
(67, 58, 24, 0, '2024-09-30 00:00:00'),
(68, 58, 54, 0, '2024-09-30 00:00:00'),
(69, 58, 8, 0, '2024-09-30 00:00:00'),
(70, 58, 22, 0, '2024-09-30 00:00:00'),
(71, 58, 27, 0, '2024-09-30 00:00:00'),
(72, 58, 28, 0, '2024-09-30 00:00:00'),
(73, 58, 29, 0, '2024-09-30 00:00:00'),
(74, 58, 30, 0, '2024-09-30 00:00:00'),
(75, 58, 31, 0, '2024-09-30 00:00:00'),
(76, 58, 33, 0, '2024-09-30 00:00:00'),
(77, 58, 34, 0, '2024-09-30 00:00:00'),
(78, 58, 36, 0, '2024-09-30 00:00:00'),
(79, 58, 42, 0, '2024-09-30 00:00:00'),
(80, 58, 56, 0, '2024-09-30 00:00:00'),
(81, 58, 1, 0, '2024-09-30 00:00:00'),
(82, 58, 2, 0, '2024-09-30 00:00:00'),
(83, 58, 10, 0, '2024-09-30 00:00:00'),
(84, 58, 3, 0, '2024-09-30 00:00:00'),
(85, 58, 4, 0, '2024-09-30 00:00:00'),
(86, 58, 5, 0, '2024-09-30 00:00:00'),
(87, 58, 7, 0, '2024-09-30 00:00:00'),
(88, 58, 12, 0, '2024-09-30 00:00:00'),
(89, 58, 15, 0, '2024-09-30 00:00:00'),
(90, 58, 43, 0, '2024-09-30 00:00:00'),
(91, 58, 44, 0, '2024-09-30 00:00:00'),
(92, 58, 45, 0, '2024-09-30 00:00:00'),
(93, 58, 46, 0, '2024-09-30 00:00:00'),
(94, 58, 47, 0, '2024-09-30 00:00:00'),
(95, 58, 48, 0, '2024-09-30 00:00:00'),
(96, 58, 49, 0, '2024-09-30 00:00:00'),
(97, 58, 50, 0, '2024-09-30 00:00:00'),
(98, 58, 51, 0, '2024-09-30 00:00:00'),
(99, 58, 52, 0, '2024-09-30 00:00:00'),
(100, 58, 53, 0, '2024-09-30 00:00:00'),
(101, 58, 6, 0, '2024-09-30 00:00:00'),
(102, 58, 11, 0, '2024-09-30 00:00:00'),
(103, 58, 14, 0, '2024-09-30 00:00:00'),
(104, 58, 13, 0, '2024-09-30 00:00:00'),
(105, 58, 20, 0, '2024-09-30 00:00:00'),
(106, 58, 21, 0, '2024-09-30 00:00:00'),
(107, 58, 32, 0, '2024-09-30 00:00:00'),
(108, 58, 37, 0, '2024-09-30 00:00:00'),
(109, 58, 38, 0, '2024-09-30 00:00:00'),
(110, 58, 39, 0, '2024-09-30 00:00:00'),
(111, 58, 40, 0, '2024-09-30 00:00:00'),
(112, 58, 41, 0, '2024-09-30 00:00:00'),
(113, 58, 25, 0, '2024-09-30 00:00:00'),
(114, 58, 26, 0, '2024-09-30 00:00:00'),
(115, 58, 35, 0, '2024-09-30 00:00:00'),
(116, 58, 55, 0, '2024-09-30 00:00:00'),
(117, 58, 9, 0, '2024-09-30 00:00:00'),
(118, 58, 16, 0, '2024-09-30 00:00:00'),
(119, 58, 17, 0, '2024-09-30 00:00:00'),
(120, 58, 18, 0, '2024-09-30 00:00:00'),
(121, 58, 19, 0, '2024-09-30 00:00:00'),
(122, 58, 23, 0, '2024-09-30 00:00:00'),
(123, 58, 24, 0, '2024-09-30 00:00:00'),
(124, 58, 54, 0, '2024-09-30 00:00:00'),
(125, 58, 8, 0, '2024-09-30 00:00:00'),
(126, 58, 22, 0, '2024-09-30 00:00:00'),
(127, 58, 27, 0, '2024-09-30 00:00:00'),
(128, 58, 28, 0, '2024-09-30 00:00:00'),
(129, 58, 29, 0, '2024-09-30 00:00:00'),
(130, 58, 30, 0, '2024-09-30 00:00:00'),
(131, 58, 31, 0, '2024-09-30 00:00:00'),
(132, 58, 33, 1, '2024-09-30 00:00:00'),
(133, 58, 34, 1, '2024-09-30 00:00:00'),
(134, 58, 36, 1, '2024-09-30 00:00:00'),
(135, 58, 42, 1, '2024-09-30 00:00:00'),
(136, 58, 56, 0, '2024-09-30 00:00:00'),
(137, 58, 1, 0, '2024-09-30 00:00:00'),
(138, 58, 2, 0, '2024-09-30 00:00:00'),
(139, 58, 10, 0, '2024-09-30 00:00:00'),
(140, 58, 3, 0, '2024-09-30 00:00:00'),
(141, 58, 4, 0, '2024-09-30 00:00:00'),
(142, 58, 5, 0, '2024-09-30 00:00:00'),
(143, 58, 7, 0, '2024-09-30 00:00:00'),
(144, 58, 12, 0, '2024-09-30 00:00:00'),
(145, 58, 15, 0, '2024-09-30 00:00:00'),
(146, 58, 43, 0, '2024-09-30 00:00:00'),
(147, 58, 44, 0, '2024-09-30 00:00:00'),
(148, 58, 45, 0, '2024-09-30 00:00:00'),
(149, 58, 46, 0, '2024-09-30 00:00:00'),
(150, 58, 47, 0, '2024-09-30 00:00:00'),
(151, 58, 48, 0, '2024-09-30 00:00:00'),
(152, 58, 49, 0, '2024-09-30 00:00:00'),
(153, 58, 50, 0, '2024-09-30 00:00:00'),
(154, 58, 51, 0, '2024-09-30 00:00:00'),
(155, 58, 52, 0, '2024-09-30 00:00:00'),
(156, 58, 53, 0, '2024-09-30 00:00:00'),
(157, 58, 6, 0, '2024-09-30 00:00:00'),
(158, 58, 11, 0, '2024-09-30 00:00:00'),
(159, 58, 14, 0, '2024-09-30 00:00:00'),
(160, 58, 13, 0, '2024-09-30 00:00:00'),
(161, 58, 20, 0, '2024-09-30 00:00:00'),
(162, 58, 21, 0, '2024-09-30 00:00:00'),
(163, 58, 32, 0, '2024-09-30 00:00:00'),
(164, 58, 37, 0, '2024-09-30 00:00:00'),
(165, 58, 38, 0, '2024-09-30 00:00:00'),
(166, 58, 39, 0, '2024-09-30 00:00:00'),
(167, 58, 40, 0, '2024-09-30 00:00:00'),
(168, 58, 41, 0, '2024-09-30 00:00:00'),
(169, 60, 25, 0, '2024-09-30 00:00:00'),
(170, 60, 26, 0, '2024-09-30 00:00:00'),
(171, 60, 35, 1, '2024-09-30 00:00:00'),
(172, 60, 55, 0, '2024-09-30 00:00:00'),
(173, 60, 9, 0, '2024-09-30 00:00:00'),
(174, 60, 16, 0, '2024-09-30 00:00:00'),
(175, 60, 17, 0, '2024-09-30 00:00:00'),
(176, 60, 18, 0, '2024-09-30 00:00:00'),
(177, 60, 19, 0, '2024-09-30 00:00:00'),
(178, 60, 23, 0, '2024-09-30 00:00:00'),
(179, 60, 24, 0, '2024-09-30 00:00:00'),
(180, 60, 54, 0, '2024-09-30 00:00:00'),
(181, 60, 8, 0, '2024-09-30 00:00:00'),
(182, 60, 22, 0, '2024-09-30 00:00:00'),
(183, 60, 27, 0, '2024-09-30 00:00:00'),
(184, 60, 28, 0, '2024-09-30 00:00:00'),
(185, 60, 29, 0, '2024-09-30 00:00:00'),
(186, 60, 30, 0, '2024-09-30 00:00:00'),
(187, 60, 31, 0, '2024-09-30 00:00:00'),
(188, 60, 33, 1, '2024-09-30 00:00:00'),
(189, 60, 34, 1, '2024-09-30 00:00:00'),
(190, 60, 36, 1, '2024-09-30 00:00:00'),
(191, 60, 42, 1, '2024-09-30 00:00:00'),
(192, 60, 56, 0, '2024-09-30 00:00:00'),
(193, 60, 1, 0, '2024-09-30 00:00:00'),
(194, 60, 2, 0, '2024-09-30 00:00:00'),
(195, 60, 10, 0, '2024-09-30 00:00:00'),
(196, 60, 3, 0, '2024-09-30 00:00:00'),
(197, 60, 4, 0, '2024-09-30 00:00:00'),
(198, 60, 5, 0, '2024-09-30 00:00:00'),
(199, 60, 7, 0, '2024-09-30 00:00:00'),
(200, 60, 12, 0, '2024-09-30 00:00:00'),
(201, 60, 15, 0, '2024-09-30 00:00:00'),
(202, 60, 43, 0, '2024-09-30 00:00:00'),
(203, 60, 44, 0, '2024-09-30 00:00:00'),
(204, 60, 45, 0, '2024-09-30 00:00:00'),
(205, 60, 46, 0, '2024-09-30 00:00:00'),
(206, 60, 47, 0, '2024-09-30 00:00:00'),
(207, 60, 48, 0, '2024-09-30 00:00:00'),
(208, 60, 49, 0, '2024-09-30 00:00:00'),
(209, 60, 50, 0, '2024-09-30 00:00:00'),
(210, 60, 51, 0, '2024-09-30 00:00:00'),
(211, 60, 52, 0, '2024-09-30 00:00:00'),
(212, 60, 53, 0, '2024-09-30 00:00:00'),
(213, 60, 6, 0, '2024-09-30 00:00:00'),
(214, 60, 11, 0, '2024-09-30 00:00:00'),
(215, 60, 14, 0, '2024-09-30 00:00:00'),
(216, 60, 13, 0, '2024-09-30 00:00:00'),
(217, 60, 20, 0, '2024-09-30 00:00:00'),
(218, 60, 21, 0, '2024-09-30 00:00:00'),
(219, 60, 32, 1, '2024-09-30 00:00:00'),
(220, 60, 37, 0, '2024-09-30 00:00:00'),
(221, 60, 38, 0, '2024-09-30 00:00:00'),
(222, 60, 39, 0, '2024-09-30 00:00:00'),
(223, 60, 40, 0, '2024-09-30 00:00:00'),
(224, 60, 41, 0, '2024-09-30 00:00:00'),
(225, 58, 25, 0, '2024-09-30 00:00:00'),
(226, 58, 26, 0, '2024-09-30 00:00:00'),
(227, 58, 35, 0, '2024-09-30 00:00:00'),
(228, 58, 55, 0, '2024-09-30 00:00:00'),
(229, 58, 9, 0, '2024-09-30 00:00:00'),
(230, 58, 16, 0, '2024-09-30 00:00:00'),
(231, 58, 17, 0, '2024-09-30 00:00:00'),
(232, 58, 18, 0, '2024-09-30 00:00:00'),
(233, 58, 19, 0, '2024-09-30 00:00:00'),
(234, 58, 23, 0, '2024-09-30 00:00:00'),
(235, 58, 24, 0, '2024-09-30 00:00:00'),
(236, 58, 54, 0, '2024-09-30 00:00:00'),
(237, 58, 8, 0, '2024-09-30 00:00:00'),
(238, 58, 22, 0, '2024-09-30 00:00:00'),
(239, 58, 27, 0, '2024-09-30 00:00:00'),
(240, 58, 28, 0, '2024-09-30 00:00:00'),
(241, 58, 29, 0, '2024-09-30 00:00:00'),
(242, 58, 30, 0, '2024-09-30 00:00:00'),
(243, 58, 31, 0, '2024-09-30 00:00:00'),
(244, 58, 33, 0, '2024-09-30 00:00:00'),
(245, 58, 34, 0, '2024-09-30 00:00:00'),
(246, 58, 36, 0, '2024-09-30 00:00:00'),
(247, 58, 42, 0, '2024-09-30 00:00:00'),
(248, 58, 56, 0, '2024-09-30 00:00:00'),
(249, 58, 1, 0, '2024-09-30 00:00:00'),
(250, 58, 2, 0, '2024-09-30 00:00:00'),
(251, 58, 10, 0, '2024-09-30 00:00:00'),
(252, 58, 3, 0, '2024-09-30 00:00:00'),
(253, 58, 4, 0, '2024-09-30 00:00:00'),
(254, 58, 5, 0, '2024-09-30 00:00:00'),
(255, 58, 7, 0, '2024-09-30 00:00:00'),
(256, 58, 12, 0, '2024-09-30 00:00:00'),
(257, 58, 15, 0, '2024-09-30 00:00:00'),
(258, 58, 43, 0, '2024-09-30 00:00:00'),
(259, 58, 44, 0, '2024-09-30 00:00:00'),
(260, 58, 45, 0, '2024-09-30 00:00:00'),
(261, 58, 46, 0, '2024-09-30 00:00:00'),
(262, 58, 47, 0, '2024-09-30 00:00:00'),
(263, 58, 48, 0, '2024-09-30 00:00:00'),
(264, 58, 49, 0, '2024-09-30 00:00:00'),
(265, 58, 50, 0, '2024-09-30 00:00:00'),
(266, 58, 51, 0, '2024-09-30 00:00:00'),
(267, 58, 52, 0, '2024-09-30 00:00:00'),
(268, 58, 53, 0, '2024-09-30 00:00:00'),
(269, 58, 6, 0, '2024-09-30 00:00:00'),
(270, 58, 11, 0, '2024-09-30 00:00:00'),
(271, 58, 14, 0, '2024-09-30 00:00:00'),
(272, 58, 13, 0, '2024-09-30 00:00:00'),
(273, 58, 20, 0, '2024-09-30 00:00:00'),
(274, 58, 21, 0, '2024-09-30 00:00:00'),
(275, 58, 32, 0, '2024-09-30 00:00:00'),
(276, 58, 37, 0, '2024-09-30 00:00:00'),
(277, 58, 38, 0, '2024-09-30 00:00:00'),
(278, 58, 39, 0, '2024-09-30 00:00:00'),
(279, 58, 40, 300, '2024-09-30 00:00:00'),
(280, 58, 41, 10, '2024-09-30 00:00:00'),
(281, 58, 25, 0, '2024-09-30 00:00:00'),
(282, 58, 26, 0, '2024-09-30 00:00:00'),
(283, 58, 35, 0, '2024-09-30 00:00:00'),
(284, 58, 55, 0, '2024-09-30 00:00:00'),
(285, 58, 9, 0, '2024-09-30 00:00:00'),
(286, 58, 16, 0, '2024-09-30 00:00:00'),
(287, 58, 17, 0, '2024-09-30 00:00:00'),
(288, 58, 18, 0, '2024-09-30 00:00:00'),
(289, 58, 19, 0, '2024-09-30 00:00:00'),
(290, 58, 23, 0, '2024-09-30 00:00:00'),
(291, 58, 24, 0, '2024-09-30 00:00:00'),
(292, 58, 54, 0, '2024-09-30 00:00:00'),
(293, 58, 8, 0, '2024-09-30 00:00:00'),
(294, 58, 22, 0, '2024-09-30 00:00:00'),
(295, 58, 27, 0, '2024-09-30 00:00:00'),
(296, 58, 28, 0, '2024-09-30 00:00:00'),
(297, 58, 29, 0, '2024-09-30 00:00:00'),
(298, 58, 30, 0, '2024-09-30 00:00:00'),
(299, 58, 31, 0, '2024-09-30 00:00:00'),
(300, 58, 33, 0, '2024-09-30 00:00:00'),
(301, 58, 34, 0, '2024-09-30 00:00:00'),
(302, 58, 36, 0, '2024-09-30 00:00:00'),
(303, 58, 42, 0, '2024-09-30 00:00:00'),
(304, 58, 56, 0, '2024-09-30 00:00:00'),
(305, 58, 1, 0, '2024-09-30 00:00:00'),
(306, 58, 2, 0, '2024-09-30 00:00:00'),
(307, 58, 10, 0, '2024-09-30 00:00:00'),
(308, 58, 3, 0, '2024-09-30 00:00:00'),
(309, 58, 4, 0, '2024-09-30 00:00:00'),
(310, 58, 5, 0, '2024-09-30 00:00:00'),
(311, 58, 7, 0, '2024-09-30 00:00:00'),
(312, 58, 12, 0, '2024-09-30 00:00:00'),
(313, 58, 15, 0, '2024-09-30 00:00:00'),
(314, 58, 43, 0, '2024-09-30 00:00:00'),
(315, 58, 44, 0, '2024-09-30 00:00:00'),
(316, 58, 45, 0, '2024-09-30 00:00:00'),
(317, 58, 46, 0, '2024-09-30 00:00:00'),
(318, 58, 47, 0, '2024-09-30 00:00:00'),
(319, 58, 48, 0, '2024-09-30 00:00:00'),
(320, 58, 49, 0, '2024-09-30 00:00:00'),
(321, 58, 50, 0, '2024-09-30 00:00:00'),
(322, 58, 51, 0, '2024-09-30 00:00:00'),
(323, 58, 52, 0, '2024-09-30 00:00:00'),
(324, 58, 53, 0, '2024-09-30 00:00:00'),
(325, 58, 6, 0, '2024-09-30 00:00:00'),
(326, 58, 11, 0, '2024-09-30 00:00:00'),
(327, 58, 14, 0, '2024-09-30 00:00:00'),
(328, 58, 13, 0, '2024-09-30 00:00:00'),
(329, 58, 20, 0, '2024-09-30 00:00:00'),
(330, 58, 21, 0, '2024-09-30 00:00:00'),
(331, 58, 32, 0, '2024-09-30 00:00:00'),
(332, 58, 37, 0, '2024-09-30 00:00:00'),
(333, 58, 38, 0, '2024-09-30 00:00:00'),
(334, 58, 39, 600, '2024-09-30 00:00:00'),
(335, 58, 40, 0, '2024-09-30 00:00:00'),
(336, 58, 41, 0, '2024-09-30 00:00:00'),
(337, 58, 25, 0, '2024-09-30 00:00:00'),
(338, 58, 26, 0, '2024-09-30 00:00:00'),
(339, 58, 35, 0, '2024-09-30 00:00:00'),
(340, 58, 55, 0, '2024-09-30 00:00:00'),
(341, 58, 9, 0, '2024-09-30 00:00:00'),
(342, 58, 16, 0, '2024-09-30 00:00:00'),
(343, 58, 17, 0, '2024-09-30 00:00:00'),
(344, 58, 18, 0, '2024-09-30 00:00:00'),
(345, 58, 19, 0, '2024-09-30 00:00:00'),
(346, 58, 23, 0, '2024-09-30 00:00:00'),
(347, 58, 24, 0, '2024-09-30 00:00:00'),
(348, 58, 54, 0, '2024-09-30 00:00:00'),
(349, 58, 8, 0, '2024-09-30 00:00:00'),
(350, 58, 22, 0, '2024-09-30 00:00:00'),
(351, 58, 27, 0, '2024-09-30 00:00:00'),
(352, 58, 28, 0, '2024-09-30 00:00:00'),
(353, 58, 29, 0, '2024-09-30 00:00:00'),
(354, 58, 30, 0, '2024-09-30 00:00:00'),
(355, 58, 31, 0, '2024-09-30 00:00:00'),
(356, 58, 33, 0, '2024-09-30 00:00:00'),
(357, 58, 34, 1, '2024-09-30 00:00:00'),
(358, 58, 36, 0, '2024-09-30 00:00:00'),
(359, 58, 42, 0, '2024-09-30 00:00:00'),
(360, 58, 56, 0, '2024-09-30 00:00:00'),
(361, 58, 1, 0, '2024-09-30 00:00:00'),
(362, 58, 2, 0, '2024-09-30 00:00:00'),
(363, 58, 10, 0, '2024-09-30 00:00:00'),
(364, 58, 3, 0, '2024-09-30 00:00:00'),
(365, 58, 4, 0, '2024-09-30 00:00:00'),
(366, 58, 5, 0, '2024-09-30 00:00:00'),
(367, 58, 7, 0, '2024-09-30 00:00:00'),
(368, 58, 12, 0, '2024-09-30 00:00:00'),
(369, 58, 15, 0, '2024-09-30 00:00:00'),
(370, 58, 43, 0, '2024-09-30 00:00:00'),
(371, 58, 44, 0, '2024-09-30 00:00:00'),
(372, 58, 45, 0, '2024-09-30 00:00:00'),
(373, 58, 46, 0, '2024-09-30 00:00:00'),
(374, 58, 47, 0, '2024-09-30 00:00:00'),
(375, 58, 48, 0, '2024-09-30 00:00:00'),
(376, 58, 49, 0, '2024-09-30 00:00:00'),
(377, 58, 50, 0, '2024-09-30 00:00:00'),
(378, 58, 51, 0, '2024-09-30 00:00:00'),
(379, 58, 52, 0, '2024-09-30 00:00:00'),
(380, 58, 53, 0, '2024-09-30 00:00:00'),
(381, 58, 6, 0, '2024-09-30 00:00:00'),
(382, 58, 11, 0, '2024-09-30 00:00:00'),
(383, 58, 14, 0, '2024-09-30 00:00:00'),
(384, 58, 13, 0, '2024-09-30 00:00:00'),
(385, 58, 20, 0, '2024-09-30 00:00:00'),
(386, 58, 21, 0, '2024-09-30 00:00:00'),
(387, 58, 32, 0, '2024-09-30 00:00:00'),
(388, 58, 37, 0, '2024-09-30 00:00:00'),
(389, 58, 38, 0, '2024-09-30 00:00:00'),
(390, 58, 39, 0, '2024-09-30 00:00:00'),
(391, 58, 40, 0, '2024-09-30 00:00:00'),
(392, 58, 41, 0, '2024-09-30 00:00:00'),
(393, 58, 25, 0, '2024-09-30 00:00:00'),
(394, 58, 26, 0, '2024-09-30 00:00:00'),
(395, 58, 35, 0, '2024-09-30 00:00:00'),
(396, 58, 55, 0, '2024-09-30 00:00:00'),
(397, 58, 9, 0, '2024-09-30 00:00:00'),
(398, 58, 16, 0, '2024-09-30 00:00:00'),
(399, 58, 17, 0, '2024-09-30 00:00:00'),
(400, 58, 18, 0, '2024-09-30 00:00:00'),
(401, 58, 19, 0, '2024-09-30 00:00:00'),
(402, 58, 23, 0, '2024-09-30 00:00:00'),
(403, 58, 24, 0, '2024-09-30 00:00:00'),
(404, 58, 54, 0, '2024-09-30 00:00:00'),
(405, 58, 8, 0, '2024-09-30 00:00:00'),
(406, 58, 22, 0, '2024-09-30 00:00:00'),
(407, 58, 27, 0, '2024-09-30 00:00:00'),
(408, 58, 28, 0, '2024-09-30 00:00:00'),
(409, 58, 29, 0, '2024-09-30 00:00:00'),
(410, 58, 30, 0, '2024-09-30 00:00:00'),
(411, 58, 31, 0, '2024-09-30 00:00:00'),
(412, 58, 33, 0, '2024-09-30 00:00:00'),
(413, 58, 34, 0, '2024-09-30 00:00:00'),
(414, 58, 36, 0, '2024-09-30 00:00:00'),
(415, 58, 42, 0, '2024-09-30 00:00:00'),
(416, 58, 56, 0, '2024-09-30 00:00:00'),
(417, 58, 1, 0, '2024-09-30 00:00:00'),
(418, 58, 2, 0, '2024-09-30 00:00:00'),
(419, 58, 10, 0, '2024-09-30 00:00:00'),
(420, 58, 3, 0, '2024-09-30 00:00:00'),
(421, 58, 4, 0, '2024-09-30 00:00:00'),
(422, 58, 5, 0, '2024-09-30 00:00:00'),
(423, 58, 7, 0, '2024-09-30 00:00:00'),
(424, 58, 12, 0, '2024-09-30 00:00:00'),
(425, 58, 15, 0, '2024-09-30 00:00:00'),
(426, 58, 43, 0, '2024-09-30 00:00:00'),
(427, 58, 44, 0, '2024-09-30 00:00:00'),
(428, 58, 45, 0, '2024-09-30 00:00:00'),
(429, 58, 46, 0, '2024-09-30 00:00:00'),
(430, 58, 47, 0, '2024-09-30 00:00:00'),
(431, 58, 48, 0, '2024-09-30 00:00:00'),
(432, 58, 49, 0, '2024-09-30 00:00:00'),
(433, 58, 50, 0, '2024-09-30 00:00:00'),
(434, 58, 51, 0, '2024-09-30 00:00:00'),
(435, 58, 52, 0, '2024-09-30 00:00:00'),
(436, 58, 53, 0, '2024-09-30 00:00:00'),
(437, 58, 6, 0, '2024-09-30 00:00:00'),
(438, 58, 11, 0, '2024-09-30 00:00:00'),
(439, 58, 14, 0, '2024-09-30 00:00:00'),
(440, 58, 13, 0, '2024-09-30 00:00:00'),
(441, 58, 20, 0, '2024-09-30 00:00:00'),
(442, 58, 21, 0, '2024-09-30 00:00:00'),
(443, 58, 32, 1, '2024-09-30 00:00:00'),
(444, 58, 37, 0, '2024-09-30 00:00:00'),
(445, 58, 38, 0, '2024-09-30 00:00:00'),
(446, 58, 39, 0, '2024-09-30 00:00:00'),
(447, 58, 40, 0, '2024-09-30 00:00:00'),
(448, 58, 41, 0, '2024-09-30 00:00:00'),
(449, 60, 25, 0, '2024-09-30 00:00:00'),
(450, 60, 26, 0, '2024-09-30 00:00:00'),
(451, 60, 35, 0, '2024-09-30 00:00:00'),
(452, 60, 55, 0, '2024-09-30 00:00:00'),
(453, 60, 9, 0, '2024-09-30 00:00:00'),
(454, 60, 16, 0, '2024-09-30 00:00:00'),
(455, 60, 17, 0, '2024-09-30 00:00:00'),
(456, 60, 18, 0, '2024-09-30 00:00:00'),
(457, 60, 19, 0, '2024-09-30 00:00:00'),
(458, 60, 23, 0, '2024-09-30 00:00:00'),
(459, 60, 24, 0, '2024-09-30 00:00:00'),
(460, 60, 54, 0, '2024-09-30 00:00:00'),
(461, 60, 8, 0, '2024-09-30 00:00:00'),
(462, 60, 22, 0, '2024-09-30 00:00:00'),
(463, 60, 27, 0, '2024-09-30 00:00:00'),
(464, 60, 28, 0, '2024-09-30 00:00:00'),
(465, 60, 29, 0, '2024-09-30 00:00:00'),
(466, 60, 30, 0, '2024-09-30 00:00:00'),
(467, 60, 31, 0, '2024-09-30 00:00:00'),
(468, 60, 33, 0, '2024-09-30 00:00:00'),
(469, 60, 34, 0, '2024-09-30 00:00:00'),
(470, 60, 36, 0, '2024-09-30 00:00:00'),
(471, 60, 42, 0, '2024-09-30 00:00:00'),
(472, 60, 56, 0, '2024-09-30 00:00:00'),
(473, 60, 1, 0, '2024-09-30 00:00:00'),
(474, 60, 2, 0, '2024-09-30 00:00:00'),
(475, 60, 10, 0, '2024-09-30 00:00:00'),
(476, 60, 3, 0, '2024-09-30 00:00:00'),
(477, 60, 4, 0, '2024-09-30 00:00:00'),
(478, 60, 5, 0, '2024-09-30 00:00:00'),
(479, 60, 7, 0, '2024-09-30 00:00:00'),
(480, 60, 12, 0, '2024-09-30 00:00:00'),
(481, 60, 15, 0, '2024-09-30 00:00:00'),
(482, 60, 43, 0, '2024-09-30 00:00:00'),
(483, 60, 44, 0, '2024-09-30 00:00:00'),
(484, 60, 45, 0, '2024-09-30 00:00:00'),
(485, 60, 46, 0, '2024-09-30 00:00:00'),
(486, 60, 47, 0, '2024-09-30 00:00:00'),
(487, 60, 48, 0, '2024-09-30 00:00:00'),
(488, 60, 49, 0, '2024-09-30 00:00:00'),
(489, 60, 50, 0, '2024-09-30 00:00:00'),
(490, 60, 51, 0, '2024-09-30 00:00:00'),
(491, 60, 52, 0, '2024-09-30 00:00:00'),
(492, 60, 53, 0, '2024-09-30 00:00:00'),
(493, 60, 6, 0, '2024-09-30 00:00:00'),
(494, 60, 11, 0, '2024-09-30 00:00:00'),
(495, 60, 14, 0, '2024-09-30 00:00:00'),
(496, 60, 13, 0, '2024-09-30 00:00:00'),
(497, 60, 20, 0, '2024-09-30 00:00:00'),
(498, 60, 21, 0, '2024-09-30 00:00:00'),
(499, 60, 32, 0, '2024-09-30 00:00:00'),
(500, 60, 37, 0, '2024-09-30 00:00:00'),
(501, 60, 38, 0, '2024-09-30 00:00:00'),
(502, 60, 39, 0, '2024-09-30 00:00:00'),
(503, 60, 40, 0, '2024-09-30 00:00:00'),
(504, 60, 41, 10, '2024-09-30 00:00:00'),
(505, 61, 25, 0, '2024-09-30 00:00:00'),
(506, 61, 26, 0, '2024-09-30 00:00:00'),
(507, 61, 35, 0, '2024-09-30 00:00:00'),
(508, 61, 55, 0, '2024-09-30 00:00:00'),
(509, 61, 9, 0, '2024-09-30 00:00:00'),
(510, 61, 16, 0, '2024-09-30 00:00:00'),
(511, 61, 17, 0, '2024-09-30 00:00:00'),
(512, 61, 18, 0, '2024-09-30 00:00:00'),
(513, 61, 19, 0, '2024-09-30 00:00:00'),
(514, 61, 23, 0, '2024-09-30 00:00:00'),
(515, 61, 24, 0, '2024-09-30 00:00:00'),
(516, 61, 54, 0, '2024-09-30 00:00:00'),
(517, 61, 8, 0, '2024-09-30 00:00:00'),
(518, 61, 22, 0, '2024-09-30 00:00:00'),
(519, 61, 27, 0, '2024-09-30 00:00:00'),
(520, 61, 28, 0, '2024-09-30 00:00:00'),
(521, 61, 29, 0, '2024-09-30 00:00:00'),
(522, 61, 30, 0, '2024-09-30 00:00:00'),
(523, 61, 31, 0, '2024-09-30 00:00:00'),
(524, 61, 33, 1, '2024-09-30 00:00:00'),
(525, 61, 34, 0, '2024-09-30 00:00:00'),
(526, 61, 36, 0, '2024-09-30 00:00:00'),
(527, 61, 42, 0, '2024-09-30 00:00:00'),
(528, 61, 56, 0, '2024-09-30 00:00:00'),
(529, 61, 1, 0, '2024-09-30 00:00:00'),
(530, 61, 2, 0, '2024-09-30 00:00:00'),
(531, 61, 10, 0, '2024-09-30 00:00:00'),
(532, 61, 3, 0, '2024-09-30 00:00:00'),
(533, 61, 4, 0, '2024-09-30 00:00:00'),
(534, 61, 5, 0, '2024-09-30 00:00:00'),
(535, 61, 7, 0, '2024-09-30 00:00:00'),
(536, 61, 12, 0, '2024-09-30 00:00:00'),
(537, 61, 15, 0, '2024-09-30 00:00:00'),
(538, 61, 43, 0, '2024-09-30 00:00:00'),
(539, 61, 44, 0, '2024-09-30 00:00:00'),
(540, 61, 45, 0, '2024-09-30 00:00:00'),
(541, 61, 46, 0, '2024-09-30 00:00:00'),
(542, 61, 47, 0, '2024-09-30 00:00:00'),
(543, 61, 48, 0, '2024-09-30 00:00:00'),
(544, 61, 49, 0, '2024-09-30 00:00:00'),
(545, 61, 50, 1, '2024-09-30 00:00:00'),
(546, 61, 51, 0, '2024-09-30 00:00:00'),
(547, 61, 52, 0, '2024-09-30 00:00:00'),
(548, 61, 53, 0, '2024-09-30 00:00:00'),
(549, 61, 6, 0, '2024-09-30 00:00:00'),
(550, 61, 11, 0, '2024-09-30 00:00:00'),
(551, 61, 14, 0, '2024-09-30 00:00:00'),
(552, 61, 13, 0, '2024-09-30 00:00:00'),
(553, 61, 20, 0, '2024-09-30 00:00:00'),
(554, 61, 21, 0, '2024-09-30 00:00:00'),
(555, 61, 32, 0, '2024-09-30 00:00:00'),
(556, 61, 37, 0, '2024-09-30 00:00:00'),
(557, 61, 38, 0, '2024-09-30 00:00:00'),
(558, 61, 39, 0, '2024-09-30 00:00:00'),
(559, 61, 40, 0, '2024-09-30 00:00:00'),
(560, 61, 41, 0, '2024-09-30 00:00:00'),
(561, 61, 25, 0, '2024-09-30 00:00:00'),
(562, 61, 26, 0, '2024-09-30 00:00:00'),
(563, 61, 35, 0, '2024-09-30 00:00:00'),
(564, 61, 55, 2, '2024-09-30 00:00:00'),
(565, 61, 9, 0, '2024-09-30 00:00:00'),
(566, 61, 16, 0, '2024-09-30 00:00:00'),
(567, 61, 17, 0, '2024-09-30 00:00:00'),
(568, 61, 18, 0, '2024-09-30 00:00:00'),
(569, 61, 19, 0, '2024-09-30 00:00:00'),
(570, 61, 23, 0, '2024-09-30 00:00:00'),
(571, 61, 24, 0, '2024-09-30 00:00:00'),
(572, 61, 54, 0, '2024-09-30 00:00:00'),
(573, 61, 8, 0, '2024-09-30 00:00:00'),
(574, 61, 22, 0, '2024-09-30 00:00:00'),
(575, 61, 27, 0, '2024-09-30 00:00:00'),
(576, 61, 28, 0, '2024-09-30 00:00:00'),
(577, 61, 29, 0, '2024-09-30 00:00:00'),
(578, 61, 30, 0, '2024-09-30 00:00:00'),
(579, 61, 31, 0, '2024-09-30 00:00:00'),
(580, 61, 33, 0, '2024-09-30 00:00:00'),
(581, 61, 34, 0, '2024-09-30 00:00:00'),
(582, 61, 36, 0, '2024-09-30 00:00:00'),
(583, 61, 42, 0, '2024-09-30 00:00:00'),
(584, 61, 56, 0, '2024-09-30 00:00:00'),
(585, 61, 1, 0, '2024-09-30 00:00:00'),
(586, 61, 2, 0, '2024-09-30 00:00:00'),
(587, 61, 10, 0, '2024-09-30 00:00:00'),
(588, 61, 3, 0, '2024-09-30 00:00:00'),
(589, 61, 4, 0, '2024-09-30 00:00:00'),
(590, 61, 5, 0, '2024-09-30 00:00:00'),
(591, 61, 7, 0, '2024-09-30 00:00:00'),
(592, 61, 12, 0, '2024-09-30 00:00:00'),
(593, 61, 15, 0, '2024-09-30 00:00:00'),
(594, 61, 43, 0, '2024-09-30 00:00:00'),
(595, 61, 44, 0, '2024-09-30 00:00:00'),
(596, 61, 45, 0, '2024-09-30 00:00:00'),
(597, 61, 46, 0, '2024-09-30 00:00:00'),
(598, 61, 47, 0, '2024-09-30 00:00:00'),
(599, 61, 48, 0, '2024-09-30 00:00:00'),
(600, 61, 49, 0, '2024-09-30 00:00:00'),
(601, 61, 50, 0, '2024-09-30 00:00:00'),
(602, 61, 51, 0, '2024-09-30 00:00:00'),
(603, 61, 52, 0, '2024-09-30 00:00:00'),
(604, 61, 53, 0, '2024-09-30 00:00:00'),
(605, 61, 6, 0, '2024-09-30 00:00:00'),
(606, 61, 11, 0, '2024-09-30 00:00:00'),
(607, 61, 14, 0, '2024-09-30 00:00:00'),
(608, 61, 13, 0, '2024-09-30 00:00:00'),
(609, 61, 20, 0, '2024-09-30 00:00:00'),
(610, 61, 21, 0, '2024-09-30 00:00:00'),
(611, 61, 32, 0, '2024-09-30 00:00:00'),
(612, 61, 37, 0, '2024-09-30 00:00:00'),
(613, 61, 38, 0, '2024-09-30 00:00:00'),
(614, 61, 39, 0, '2024-09-30 00:00:00'),
(615, 61, 40, 0, '2024-09-30 00:00:00'),
(616, 61, 41, 6, '2024-09-30 00:00:00'),
(617, 61, 25, 0, '2024-09-30 00:00:00'),
(618, 61, 26, 0, '2024-09-30 00:00:00'),
(619, 61, 35, 0, '2024-09-30 00:00:00'),
(620, 61, 55, 0, '2024-09-30 00:00:00'),
(621, 61, 9, 0, '2024-09-30 00:00:00'),
(622, 61, 16, 0, '2024-09-30 00:00:00'),
(623, 61, 17, 0, '2024-09-30 00:00:00'),
(624, 61, 18, 0, '2024-09-30 00:00:00'),
(625, 61, 19, 0, '2024-09-30 00:00:00'),
(626, 61, 23, 0, '2024-09-30 00:00:00'),
(627, 61, 24, 0, '2024-09-30 00:00:00'),
(628, 61, 54, 0, '2024-09-30 00:00:00'),
(629, 61, 8, 0, '2024-09-30 00:00:00'),
(630, 61, 22, 0, '2024-09-30 00:00:00'),
(631, 61, 27, 0, '2024-09-30 00:00:00'),
(632, 61, 28, 0, '2024-09-30 00:00:00'),
(633, 61, 29, 0, '2024-09-30 00:00:00'),
(634, 61, 30, 0, '2024-09-30 00:00:00'),
(635, 61, 31, 0, '2024-09-30 00:00:00'),
(636, 61, 33, 0, '2024-09-30 00:00:00'),
(637, 61, 34, 0, '2024-09-30 00:00:00'),
(638, 61, 36, 0, '2024-09-30 00:00:00'),
(639, 61, 42, 0, '2024-09-30 00:00:00'),
(640, 61, 56, 0, '2024-09-30 00:00:00'),
(641, 61, 1, 0, '2024-09-30 00:00:00'),
(642, 61, 2, 0, '2024-09-30 00:00:00'),
(643, 61, 10, 0, '2024-09-30 00:00:00'),
(644, 61, 3, 0, '2024-09-30 00:00:00'),
(645, 61, 4, 0, '2024-09-30 00:00:00'),
(646, 61, 5, 0, '2024-09-30 00:00:00'),
(647, 61, 7, 0, '2024-09-30 00:00:00'),
(648, 61, 12, 0, '2024-09-30 00:00:00'),
(649, 61, 15, 0, '2024-09-30 00:00:00'),
(650, 61, 43, 0, '2024-09-30 00:00:00'),
(651, 61, 44, 0, '2024-09-30 00:00:00'),
(652, 61, 45, 0, '2024-09-30 00:00:00'),
(653, 61, 46, 0, '2024-09-30 00:00:00'),
(654, 61, 47, 0, '2024-09-30 00:00:00'),
(655, 61, 48, 0, '2024-09-30 00:00:00'),
(656, 61, 49, 0, '2024-09-30 00:00:00'),
(657, 61, 50, 0, '2024-09-30 00:00:00'),
(658, 61, 51, 0, '2024-09-30 00:00:00'),
(659, 61, 52, 0, '2024-09-30 00:00:00'),
(660, 61, 53, 0, '2024-09-30 00:00:00'),
(661, 61, 6, 0, '2024-09-30 00:00:00'),
(662, 61, 11, 0, '2024-09-30 00:00:00'),
(663, 61, 14, 0, '2024-09-30 00:00:00'),
(664, 61, 13, 0, '2024-09-30 00:00:00'),
(665, 61, 20, 0, '2024-09-30 00:00:00'),
(666, 61, 21, 0, '2024-09-30 00:00:00'),
(667, 61, 32, 0, '2024-09-30 00:00:00'),
(668, 61, 37, 0, '2024-09-30 00:00:00'),
(669, 61, 38, 0, '2024-09-30 00:00:00'),
(670, 61, 39, 1, '2024-09-30 00:00:00'),
(671, 61, 40, 0, '2024-09-30 00:00:00'),
(672, 61, 41, 0, '2024-09-30 00:00:00'),
(673, 60, 25, 0, '2024-10-02 00:00:00'),
(674, 60, 26, 1, '2024-10-02 00:00:00'),
(675, 60, 35, 0, '2024-10-02 00:00:00'),
(676, 60, 55, 0, '2024-10-02 00:00:00'),
(677, 60, 9, 0, '2024-10-02 00:00:00'),
(678, 60, 16, 0, '2024-10-02 00:00:00'),
(679, 60, 17, 0, '2024-10-02 00:00:00'),
(680, 60, 18, 0, '2024-10-02 00:00:00'),
(681, 60, 19, 0, '2024-10-02 00:00:00'),
(682, 60, 23, 0, '2024-10-02 00:00:00'),
(683, 60, 24, 0, '2024-10-02 00:00:00'),
(684, 60, 54, 0, '2024-10-02 00:00:00'),
(685, 60, 8, 0, '2024-10-02 00:00:00'),
(686, 60, 22, 0, '2024-10-02 00:00:00'),
(687, 60, 27, 0, '2024-10-02 00:00:00'),
(688, 60, 28, 0, '2024-10-02 00:00:00'),
(689, 60, 29, 0, '2024-10-02 00:00:00'),
(690, 60, 30, 1, '2024-10-02 00:00:00'),
(691, 60, 31, 1, '2024-10-02 00:00:00'),
(692, 60, 33, 0, '2024-10-02 00:00:00'),
(693, 60, 34, 0, '2024-10-02 00:00:00'),
(694, 60, 36, 0, '2024-10-02 00:00:00'),
(695, 60, 42, 0, '2024-10-02 00:00:00'),
(696, 60, 56, 5, '2024-10-02 00:00:00'),
(697, 60, 1, 0, '2024-10-02 00:00:00'),
(698, 60, 2, 0, '2024-10-02 00:00:00'),
(699, 60, 10, 0, '2024-10-02 00:00:00'),
(700, 60, 3, 0, '2024-10-02 00:00:00'),
(701, 60, 4, 0, '2024-10-02 00:00:00'),
(702, 60, 5, 0, '2024-10-02 00:00:00'),
(703, 60, 7, 0, '2024-10-02 00:00:00'),
(704, 60, 12, 0, '2024-10-02 00:00:00'),
(705, 60, 15, 0, '2024-10-02 00:00:00'),
(706, 60, 43, 0, '2024-10-02 00:00:00'),
(707, 60, 44, 0, '2024-10-02 00:00:00'),
(708, 60, 45, 0, '2024-10-02 00:00:00'),
(709, 60, 46, 0, '2024-10-02 00:00:00'),
(710, 60, 47, 0, '2024-10-02 00:00:00'),
(711, 60, 48, 0, '2024-10-02 00:00:00'),
(712, 60, 49, 0, '2024-10-02 00:00:00'),
(713, 60, 50, 0, '2024-10-02 00:00:00'),
(714, 60, 51, 0, '2024-10-02 00:00:00'),
(715, 60, 52, 0, '2024-10-02 00:00:00'),
(716, 60, 53, 0, '2024-10-02 00:00:00'),
(717, 60, 6, 0, '2024-10-02 00:00:00'),
(718, 60, 11, 0, '2024-10-02 00:00:00'),
(719, 60, 14, 0, '2024-10-02 00:00:00'),
(720, 60, 13, 0, '2024-10-02 00:00:00'),
(721, 60, 20, 0, '2024-10-02 00:00:00'),
(722, 60, 21, 1, '2024-10-02 00:00:00'),
(723, 60, 32, 0, '2024-10-02 00:00:00'),
(724, 60, 37, 0, '2024-10-02 00:00:00'),
(725, 60, 38, 0, '2024-10-02 00:00:00'),
(726, 60, 39, 6, '2024-10-02 00:00:00'),
(727, 60, 40, 30, '2024-10-02 00:00:00'),
(728, 60, 41, 6, '2024-10-02 00:00:00'),
(729, 60, 25, 0, '2024-10-02 00:00:00'),
(730, 60, 26, 0, '2024-10-02 00:00:00'),
(731, 60, 35, 0, '2024-10-02 00:00:00'),
(732, 60, 55, 2, '2024-10-02 00:00:00'),
(733, 60, 9, 0, '2024-10-02 00:00:00'),
(734, 60, 16, 0, '2024-10-02 00:00:00'),
(735, 60, 17, 0, '2024-10-02 00:00:00'),
(736, 60, 18, 0, '2024-10-02 00:00:00'),
(737, 60, 19, 0, '2024-10-02 00:00:00'),
(738, 60, 23, 0, '2024-10-02 00:00:00'),
(739, 60, 24, 0, '2024-10-02 00:00:00'),
(740, 60, 54, 0, '2024-10-02 00:00:00'),
(741, 60, 8, 0, '2024-10-02 00:00:00'),
(742, 60, 22, 0, '2024-10-02 00:00:00'),
(743, 60, 27, 0, '2024-10-02 00:00:00'),
(744, 60, 28, 0, '2024-10-02 00:00:00'),
(745, 60, 29, 0, '2024-10-02 00:00:00'),
(746, 60, 30, 0, '2024-10-02 00:00:00'),
(747, 60, 31, 0, '2024-10-02 00:00:00'),
(748, 60, 33, 0, '2024-10-02 00:00:00'),
(749, 60, 34, 0, '2024-10-02 00:00:00'),
(750, 60, 36, 0, '2024-10-02 00:00:00'),
(751, 60, 42, 0, '2024-10-02 00:00:00'),
(752, 60, 56, 0, '2024-10-02 00:00:00'),
(753, 60, 1, 0, '2024-10-02 00:00:00'),
(754, 60, 2, 0, '2024-10-02 00:00:00'),
(755, 60, 10, 0, '2024-10-02 00:00:00'),
(756, 60, 3, 0, '2024-10-02 00:00:00'),
(757, 60, 4, 0, '2024-10-02 00:00:00'),
(758, 60, 5, 0, '2024-10-02 00:00:00'),
(759, 60, 7, 0, '2024-10-02 00:00:00'),
(760, 60, 12, 0, '2024-10-02 00:00:00'),
(761, 60, 15, 0, '2024-10-02 00:00:00'),
(762, 60, 43, 0, '2024-10-02 00:00:00'),
(763, 60, 44, 0, '2024-10-02 00:00:00'),
(764, 60, 45, 0, '2024-10-02 00:00:00'),
(765, 60, 46, 0, '2024-10-02 00:00:00'),
(766, 60, 47, 0, '2024-10-02 00:00:00'),
(767, 60, 48, 0, '2024-10-02 00:00:00'),
(768, 60, 49, 0, '2024-10-02 00:00:00'),
(769, 60, 50, 0, '2024-10-02 00:00:00'),
(770, 60, 51, 0, '2024-10-02 00:00:00'),
(771, 60, 52, 0, '2024-10-02 00:00:00'),
(772, 60, 53, 0, '2024-10-02 00:00:00'),
(773, 60, 6, 0, '2024-10-02 00:00:00'),
(774, 60, 11, 0, '2024-10-02 00:00:00'),
(775, 60, 14, 0, '2024-10-02 00:00:00'),
(776, 60, 13, 0, '2024-10-02 00:00:00'),
(777, 60, 20, 0, '2024-10-02 00:00:00'),
(778, 60, 21, 0, '2024-10-02 00:00:00'),
(779, 60, 32, 0, '2024-10-02 00:00:00'),
(780, 60, 37, 0, '2024-10-02 00:00:00'),
(781, 60, 38, 0, '2024-10-02 00:00:00'),
(782, 60, 39, 0, '2024-10-02 00:00:00'),
(783, 60, 40, 0, '2024-10-02 00:00:00'),
(784, 60, 41, 0, '2024-10-02 00:00:00'),
(785, 60, 25, 0, '2024-10-02 00:00:00'),
(786, 60, 26, 0, '2024-10-02 00:00:00'),
(787, 60, 35, 0, '2024-10-02 00:00:00'),
(788, 60, 55, 0, '2024-10-02 00:00:00'),
(789, 60, 9, 0, '2024-10-02 00:00:00'),
(790, 60, 16, 0, '2024-10-02 00:00:00'),
(791, 60, 17, 0, '2024-10-02 00:00:00'),
(792, 60, 18, 0, '2024-10-02 00:00:00'),
(793, 60, 19, 2, '2024-10-02 00:00:00'),
(794, 60, 23, 0, '2024-10-02 00:00:00'),
(795, 60, 24, 0, '2024-10-02 00:00:00'),
(796, 60, 54, 0, '2024-10-02 00:00:00'),
(797, 60, 8, 0, '2024-10-02 00:00:00'),
(798, 60, 22, 0, '2024-10-02 00:00:00'),
(799, 60, 27, 0, '2024-10-02 00:00:00'),
(800, 60, 28, 0, '2024-10-02 00:00:00'),
(801, 60, 29, 0, '2024-10-02 00:00:00'),
(802, 60, 30, 0, '2024-10-02 00:00:00'),
(803, 60, 31, 0, '2024-10-02 00:00:00'),
(804, 60, 33, 0, '2024-10-02 00:00:00'),
(805, 60, 34, 0, '2024-10-02 00:00:00'),
(806, 60, 36, 0, '2024-10-02 00:00:00'),
(807, 60, 42, 0, '2024-10-02 00:00:00'),
(808, 60, 56, 0, '2024-10-02 00:00:00'),
(809, 60, 1, 0, '2024-10-02 00:00:00'),
(810, 60, 2, 0, '2024-10-02 00:00:00'),
(811, 60, 10, 0, '2024-10-02 00:00:00'),
(812, 60, 3, 0, '2024-10-02 00:00:00'),
(813, 60, 4, 0, '2024-10-02 00:00:00'),
(814, 60, 5, 0, '2024-10-02 00:00:00'),
(815, 60, 7, 0, '2024-10-02 00:00:00'),
(816, 60, 12, 0, '2024-10-02 00:00:00'),
(817, 60, 15, 0, '2024-10-02 00:00:00'),
(818, 60, 43, 0, '2024-10-02 00:00:00'),
(819, 60, 44, 0, '2024-10-02 00:00:00'),
(820, 60, 45, 0, '2024-10-02 00:00:00'),
(821, 60, 46, 0, '2024-10-02 00:00:00'),
(822, 60, 47, 0, '2024-10-02 00:00:00'),
(823, 60, 48, 0, '2024-10-02 00:00:00'),
(824, 60, 49, 0, '2024-10-02 00:00:00'),
(825, 60, 50, 0, '2024-10-02 00:00:00'),
(826, 60, 51, 0, '2024-10-02 00:00:00'),
(827, 60, 52, 0, '2024-10-02 00:00:00'),
(828, 60, 53, 0, '2024-10-02 00:00:00'),
(829, 60, 6, 0, '2024-10-02 00:00:00'),
(830, 60, 11, 0, '2024-10-02 00:00:00'),
(831, 60, 14, 0, '2024-10-02 00:00:00'),
(832, 60, 13, 0, '2024-10-02 00:00:00'),
(833, 60, 20, 0, '2024-10-02 00:00:00'),
(834, 60, 21, 0, '2024-10-02 00:00:00'),
(835, 60, 32, 0, '2024-10-02 00:00:00'),
(836, 60, 37, 0, '2024-10-02 00:00:00'),
(837, 60, 38, 0, '2024-10-02 00:00:00'),
(838, 60, 39, 0, '2024-10-02 00:00:00'),
(839, 60, 40, 0, '2024-10-02 00:00:00'),
(840, 60, 41, 0, '2024-10-02 00:00:00'),
(841, 60, 25, 5, '2024-10-03 00:00:00'),
(842, 60, 26, 0, '2024-10-03 00:00:00'),
(843, 60, 35, 0, '2024-10-03 00:00:00'),
(844, 60, 55, 0, '2024-10-03 00:00:00'),
(845, 60, 9, 0, '2024-10-03 00:00:00'),
(846, 60, 16, 0, '2024-10-03 00:00:00'),
(847, 60, 17, 0, '2024-10-03 00:00:00'),
(848, 60, 18, 0, '2024-10-03 00:00:00'),
(849, 60, 19, 0, '2024-10-03 00:00:00'),
(850, 60, 23, 0, '2024-10-03 00:00:00'),
(851, 60, 24, 0, '2024-10-03 00:00:00'),
(852, 60, 54, 0, '2024-10-03 00:00:00'),
(853, 60, 8, 0, '2024-10-03 00:00:00'),
(854, 60, 22, 0, '2024-10-03 00:00:00'),
(855, 60, 27, 0, '2024-10-03 00:00:00'),
(856, 60, 28, 0, '2024-10-03 00:00:00'),
(857, 60, 29, 0, '2024-10-03 00:00:00'),
(858, 60, 30, 0, '2024-10-03 00:00:00'),
(859, 60, 31, 0, '2024-10-03 00:00:00'),
(860, 60, 33, 0, '2024-10-03 00:00:00'),
(861, 60, 34, 0, '2024-10-03 00:00:00'),
(862, 60, 36, 0, '2024-10-03 00:00:00'),
(863, 60, 42, 0, '2024-10-03 00:00:00'),
(864, 60, 56, 0, '2024-10-03 00:00:00'),
(865, 60, 1, 0, '2024-10-03 00:00:00'),
(866, 60, 2, 0, '2024-10-03 00:00:00'),
(867, 60, 10, 0, '2024-10-03 00:00:00'),
(868, 60, 3, 0, '2024-10-03 00:00:00'),
(869, 60, 4, 0, '2024-10-03 00:00:00'),
(870, 60, 5, 0, '2024-10-03 00:00:00'),
(871, 60, 7, 0, '2024-10-03 00:00:00'),
(872, 60, 12, 0, '2024-10-03 00:00:00'),
(873, 60, 15, 0, '2024-10-03 00:00:00'),
(874, 60, 43, 0, '2024-10-03 00:00:00'),
(875, 60, 44, 0, '2024-10-03 00:00:00'),
(876, 60, 45, 0, '2024-10-03 00:00:00'),
(877, 60, 46, 0, '2024-10-03 00:00:00'),
(878, 60, 47, 0, '2024-10-03 00:00:00'),
(879, 60, 48, 0, '2024-10-03 00:00:00'),
(880, 60, 49, 0, '2024-10-03 00:00:00'),
(881, 60, 50, 0, '2024-10-03 00:00:00'),
(882, 60, 51, 0, '2024-10-03 00:00:00'),
(883, 60, 52, 0, '2024-10-03 00:00:00'),
(884, 60, 53, 0, '2024-10-03 00:00:00'),
(885, 60, 6, 0, '2024-10-03 00:00:00'),
(886, 60, 11, 0, '2024-10-03 00:00:00'),
(887, 60, 14, 0, '2024-10-03 00:00:00'),
(888, 60, 13, 0, '2024-10-03 00:00:00'),
(889, 60, 20, 0, '2024-10-03 00:00:00'),
(890, 60, 21, 0, '2024-10-03 00:00:00'),
(891, 60, 32, 0, '2024-10-03 00:00:00'),
(892, 60, 37, 0, '2024-10-03 00:00:00'),
(893, 60, 38, 0, '2024-10-03 00:00:00'),
(894, 60, 39, 0, '2024-10-03 00:00:00'),
(895, 60, 40, 0, '2024-10-03 00:00:00'),
(896, 60, 41, 0, '2024-10-03 00:00:00'),
(897, 58, 25, 0, '2024-10-05 00:00:00'),
(898, 58, 26, 0, '2024-10-05 00:00:00'),
(899, 58, 35, 0, '2024-10-05 00:00:00'),
(900, 58, 55, 3, '2024-10-05 00:00:00'),
(901, 58, 9, 0, '2024-10-05 00:00:00'),
(902, 58, 16, 0, '2024-10-05 00:00:00'),
(903, 58, 17, 0, '2024-10-05 00:00:00'),
(904, 58, 18, 0, '2024-10-05 00:00:00'),
(905, 58, 19, 0, '2024-10-05 00:00:00'),
(906, 58, 23, 0, '2024-10-05 00:00:00'),
(907, 58, 24, 0, '2024-10-05 00:00:00'),
(908, 58, 54, 0, '2024-10-05 00:00:00'),
(909, 58, 8, 0, '2024-10-05 00:00:00'),
(910, 58, 22, 0, '2024-10-05 00:00:00'),
(911, 58, 27, 0, '2024-10-05 00:00:00'),
(912, 58, 28, 0, '2024-10-05 00:00:00'),
(913, 58, 29, 0, '2024-10-05 00:00:00'),
(914, 58, 30, 0, '2024-10-05 00:00:00'),
(915, 58, 31, 0, '2024-10-05 00:00:00'),
(916, 58, 33, 0, '2024-10-05 00:00:00'),
(917, 58, 34, 0, '2024-10-05 00:00:00'),
(918, 58, 36, 0, '2024-10-05 00:00:00'),
(919, 58, 42, 0, '2024-10-05 00:00:00'),
(920, 58, 56, 0, '2024-10-05 00:00:00'),
(921, 58, 1, 0, '2024-10-05 00:00:00'),
(922, 58, 2, 0, '2024-10-05 00:00:00'),
(923, 58, 10, 0, '2024-10-05 00:00:00'),
(924, 58, 3, 0, '2024-10-05 00:00:00'),
(925, 58, 4, 0, '2024-10-05 00:00:00'),
(926, 58, 5, 0, '2024-10-05 00:00:00'),
(927, 58, 7, 0, '2024-10-05 00:00:00'),
(928, 58, 12, 0, '2024-10-05 00:00:00'),
(929, 58, 15, 0, '2024-10-05 00:00:00'),
(930, 58, 43, 0, '2024-10-05 00:00:00'),
(931, 58, 44, 0, '2024-10-05 00:00:00'),
(932, 58, 45, 0, '2024-10-05 00:00:00'),
(933, 58, 46, 0, '2024-10-05 00:00:00'),
(934, 58, 47, 0, '2024-10-05 00:00:00'),
(935, 58, 48, 0, '2024-10-05 00:00:00'),
(936, 58, 49, 0, '2024-10-05 00:00:00'),
(937, 58, 50, 0, '2024-10-05 00:00:00'),
(938, 58, 51, 0, '2024-10-05 00:00:00'),
(939, 58, 52, 0, '2024-10-05 00:00:00'),
(940, 58, 53, 0, '2024-10-05 00:00:00'),
(941, 58, 6, 0, '2024-10-05 00:00:00'),
(942, 58, 11, 3, '2024-10-05 00:00:00'),
(943, 58, 14, 0, '2024-10-05 00:00:00'),
(944, 58, 13, 0, '2024-10-05 00:00:00'),
(945, 58, 20, 0, '2024-10-05 00:00:00'),
(946, 58, 21, 0, '2024-10-05 00:00:00'),
(947, 58, 32, 0, '2024-10-05 00:00:00'),
(948, 58, 37, 0, '2024-10-05 00:00:00'),
(949, 58, 38, 0, '2024-10-05 00:00:00'),
(950, 58, 39, 2, '2024-10-05 00:00:00'),
(951, 58, 40, 30, '2024-10-05 00:00:00'),
(952, 58, 41, 6, '2024-10-05 00:00:00'),
(953, 58, 25, 0, '2024-10-05 00:00:00'),
(954, 58, 26, 0, '2024-10-05 00:00:00'),
(955, 58, 35, 0, '2024-10-05 00:00:00'),
(956, 58, 55, 0, '2024-10-05 00:00:00'),
(957, 58, 9, 0, '2024-10-05 00:00:00'),
(958, 58, 16, 0, '2024-10-05 00:00:00'),
(959, 58, 17, 0, '2024-10-05 00:00:00'),
(960, 58, 18, 0, '2024-10-05 00:00:00'),
(961, 58, 19, 0, '2024-10-05 00:00:00'),
(962, 58, 23, 0, '2024-10-05 00:00:00'),
(963, 58, 24, 0, '2024-10-05 00:00:00'),
(964, 58, 54, 0, '2024-10-05 00:00:00'),
(965, 58, 8, 0, '2024-10-05 00:00:00'),
(966, 58, 22, 0, '2024-10-05 00:00:00'),
(967, 58, 27, 0, '2024-10-05 00:00:00'),
(968, 58, 28, 0, '2024-10-05 00:00:00'),
(969, 58, 29, 0, '2024-10-05 00:00:00'),
(970, 58, 30, 0, '2024-10-05 00:00:00'),
(971, 58, 31, 0, '2024-10-05 00:00:00'),
(972, 58, 33, 0, '2024-10-05 00:00:00'),
(973, 58, 34, 0, '2024-10-05 00:00:00'),
(974, 58, 36, 0, '2024-10-05 00:00:00'),
(975, 58, 42, 0, '2024-10-05 00:00:00'),
(976, 58, 56, 0, '2024-10-05 00:00:00'),
(977, 58, 1, 0, '2024-10-05 00:00:00'),
(978, 58, 2, 0, '2024-10-05 00:00:00'),
(979, 58, 10, 0, '2024-10-05 00:00:00'),
(980, 58, 3, 0, '2024-10-05 00:00:00'),
(981, 58, 4, 0, '2024-10-05 00:00:00'),
(982, 58, 5, 0, '2024-10-05 00:00:00'),
(983, 58, 7, 0, '2024-10-05 00:00:00'),
(984, 58, 12, 0, '2024-10-05 00:00:00'),
(985, 58, 15, 0, '2024-10-05 00:00:00'),
(986, 58, 43, 0, '2024-10-05 00:00:00'),
(987, 58, 44, 0, '2024-10-05 00:00:00'),
(988, 58, 45, 0, '2024-10-05 00:00:00'),
(989, 58, 46, 0, '2024-10-05 00:00:00'),
(990, 58, 47, 0, '2024-10-05 00:00:00'),
(991, 58, 48, 0, '2024-10-05 00:00:00'),
(992, 58, 49, 0, '2024-10-05 00:00:00'),
(993, 58, 50, 0, '2024-10-05 00:00:00'),
(994, 58, 51, 0, '2024-10-05 00:00:00'),
(995, 58, 52, 0, '2024-10-05 00:00:00'),
(996, 58, 53, 0, '2024-10-05 00:00:00'),
(997, 58, 6, 0, '2024-10-05 00:00:00'),
(998, 58, 11, 0, '2024-10-05 00:00:00'),
(999, 58, 14, 0, '2024-10-05 00:00:00'),
(1000, 58, 13, 0, '2024-10-05 00:00:00'),
(1001, 58, 20, 0, '2024-10-05 00:00:00'),
(1002, 58, 21, 0, '2024-10-05 00:00:00'),
(1003, 58, 32, 0, '2024-10-05 00:00:00'),
(1004, 58, 37, 0, '2024-10-05 00:00:00'),
(1005, 58, 38, 0, '2024-10-05 00:00:00'),
(1006, 58, 39, 0, '2024-10-05 00:00:00'),
(1007, 58, 40, 0, '2024-10-05 00:00:00'),
(1008, 58, 41, 0, '2024-10-05 00:00:00'),
(1009, 58, 25, 0, '2024-10-05 00:00:00'),
(1010, 58, 26, 0, '2024-10-05 00:00:00'),
(1011, 58, 35, 0, '2024-10-05 00:00:00'),
(1012, 58, 55, 4, '2024-10-05 00:00:00'),
(1013, 58, 9, 0, '2024-10-05 00:00:00'),
(1014, 58, 16, 0, '2024-10-05 00:00:00'),
(1015, 58, 17, 0, '2024-10-05 00:00:00'),
(1016, 58, 18, 0, '2024-10-05 00:00:00'),
(1017, 58, 19, 0, '2024-10-05 00:00:00'),
(1018, 58, 23, 0, '2024-10-05 00:00:00'),
(1019, 58, 24, 0, '2024-10-05 00:00:00'),
(1020, 58, 54, 0, '2024-10-05 00:00:00'),
(1021, 58, 8, 0, '2024-10-05 00:00:00'),
(1022, 58, 22, 0, '2024-10-05 00:00:00'),
(1023, 58, 27, 1, '2024-10-05 00:00:00'),
(1024, 58, 28, 0, '2024-10-05 00:00:00'),
(1025, 58, 29, 0, '2024-10-05 00:00:00'),
(1026, 58, 30, 1, '2024-10-05 00:00:00'),
(1027, 58, 31, 0, '2024-10-05 00:00:00'),
(1028, 58, 33, 0, '2024-10-05 00:00:00'),
(1029, 58, 34, 0, '2024-10-05 00:00:00'),
(1030, 58, 36, 0, '2024-10-05 00:00:00'),
(1031, 58, 42, 0, '2024-10-05 00:00:00'),
(1032, 58, 56, 0, '2024-10-05 00:00:00'),
(1033, 58, 1, 0, '2024-10-05 00:00:00'),
(1034, 58, 2, 0, '2024-10-05 00:00:00'),
(1035, 58, 10, 0, '2024-10-05 00:00:00'),
(1036, 58, 3, 0, '2024-10-05 00:00:00'),
(1037, 58, 4, 0, '2024-10-05 00:00:00'),
(1038, 58, 5, 0, '2024-10-05 00:00:00'),
(1039, 58, 7, 0, '2024-10-05 00:00:00'),
(1040, 58, 12, 0, '2024-10-05 00:00:00'),
(1041, 58, 15, 0, '2024-10-05 00:00:00'),
(1042, 58, 43, 0, '2024-10-05 00:00:00'),
(1043, 58, 44, 0, '2024-10-05 00:00:00'),
(1044, 58, 45, 0, '2024-10-05 00:00:00'),
(1045, 58, 46, 0, '2024-10-05 00:00:00'),
(1046, 58, 47, 0, '2024-10-05 00:00:00'),
(1047, 58, 48, 0, '2024-10-05 00:00:00'),
(1048, 58, 49, 0, '2024-10-05 00:00:00'),
(1049, 58, 50, 0, '2024-10-05 00:00:00'),
(1050, 58, 51, 0, '2024-10-05 00:00:00'),
(1051, 58, 52, 0, '2024-10-05 00:00:00'),
(1052, 58, 53, 0, '2024-10-05 00:00:00'),
(1053, 58, 6, 0, '2024-10-05 00:00:00'),
(1054, 58, 11, 0, '2024-10-05 00:00:00'),
(1055, 58, 14, 0, '2024-10-05 00:00:00'),
(1056, 58, 13, 0, '2024-10-05 00:00:00'),
(1057, 58, 20, 0, '2024-10-05 00:00:00'),
(1058, 58, 21, 0, '2024-10-05 00:00:00'),
(1059, 58, 32, 0, '2024-10-05 00:00:00'),
(1060, 58, 37, 0, '2024-10-05 00:00:00'),
(1061, 58, 38, 0, '2024-10-05 00:00:00'),
(1062, 58, 39, 1, '2024-10-05 00:00:00'),
(1063, 58, 40, 30, '2024-10-05 00:00:00'),
(1064, 58, 41, 0, '2024-10-05 00:00:00'),
(1065, 58, 25, 0, '2025-05-13 00:00:00'),
(1066, 58, 26, 0, '2025-05-13 00:00:00'),
(1067, 58, 35, 0, '2025-05-13 00:00:00'),
(1068, 58, 55, 0, '2025-05-13 00:00:00'),
(1069, 58, 9, 0, '2025-05-13 00:00:00'),
(1070, 58, 16, 0, '2025-05-13 00:00:00'),
(1071, 58, 17, 0, '2025-05-13 00:00:00'),
(1072, 58, 18, 0, '2025-05-13 00:00:00'),
(1073, 58, 19, 0, '2025-05-13 00:00:00'),
(1074, 58, 23, 0, '2025-05-13 00:00:00'),
(1075, 58, 24, 0, '2025-05-13 00:00:00'),
(1076, 58, 54, 0, '2025-05-13 00:00:00'),
(1077, 58, 8, 0, '2025-05-13 00:00:00'),
(1078, 58, 22, 0, '2025-05-13 00:00:00'),
(1079, 58, 27, 0, '2025-05-13 00:00:00'),
(1080, 58, 28, 0, '2025-05-13 00:00:00'),
(1081, 58, 29, 0, '2025-05-13 00:00:00'),
(1082, 58, 30, 0, '2025-05-13 00:00:00'),
(1083, 58, 31, 0, '2025-05-13 00:00:00'),
(1084, 58, 33, 0, '2025-05-13 00:00:00'),
(1085, 58, 34, 0, '2025-05-13 00:00:00'),
(1086, 58, 36, 0, '2025-05-13 00:00:00'),
(1087, 58, 42, 0, '2025-05-13 00:00:00'),
(1088, 58, 56, 0, '2025-05-13 00:00:00'),
(1089, 58, 1, 0, '2025-05-13 00:00:00'),
(1090, 58, 2, 0, '2025-05-13 00:00:00'),
(1091, 58, 10, 0, '2025-05-13 00:00:00'),
(1092, 58, 3, 0, '2025-05-13 00:00:00'),
(1093, 58, 4, 0, '2025-05-13 00:00:00'),
(1094, 58, 5, 0, '2025-05-13 00:00:00'),
(1095, 58, 7, 0, '2025-05-13 00:00:00'),
(1096, 58, 12, 0, '2025-05-13 00:00:00'),
(1097, 58, 15, 0, '2025-05-13 00:00:00'),
(1098, 58, 43, 0, '2025-05-13 00:00:00'),
(1099, 58, 44, 0, '2025-05-13 00:00:00'),
(1100, 58, 45, 0, '2025-05-13 00:00:00'),
(1101, 58, 46, 0, '2025-05-13 00:00:00'),
(1102, 58, 47, 0, '2025-05-13 00:00:00'),
(1103, 58, 48, 0, '2025-05-13 00:00:00'),
(1104, 58, 49, 0, '2025-05-13 00:00:00'),
(1105, 58, 50, 0, '2025-05-13 00:00:00'),
(1106, 58, 51, 0, '2025-05-13 00:00:00'),
(1107, 58, 52, 0, '2025-05-13 00:00:00'),
(1108, 58, 53, 0, '2025-05-13 00:00:00'),
(1109, 58, 6, 0, '2025-05-13 00:00:00'),
(1110, 58, 11, 0, '2025-05-13 00:00:00'),
(1111, 58, 14, 0, '2025-05-13 00:00:00'),
(1112, 58, 13, 0, '2025-05-13 00:00:00'),
(1113, 58, 20, 0, '2025-05-13 00:00:00'),
(1114, 58, 21, 0, '2025-05-13 00:00:00'),
(1115, 58, 32, 0, '2025-05-13 00:00:00'),
(1116, 58, 37, 0, '2025-05-13 00:00:00'),
(1117, 58, 38, 0, '2025-05-13 00:00:00'),
(1118, 58, 39, 0, '2025-05-13 00:00:00'),
(1119, 58, 40, 0, '2025-05-13 00:00:00'),
(1120, 58, 41, 0, '2025-05-13 00:00:00'),
(1121, 58, 25, 0, '2025-05-13 00:00:00'),
(1122, 58, 26, 1, '2025-05-13 00:00:00'),
(1123, 58, 35, 0, '2025-05-13 00:00:00'),
(1124, 58, 55, 5, '2025-05-13 00:00:00'),
(1125, 58, 9, 0, '2025-05-13 00:00:00'),
(1126, 58, 16, 0, '2025-05-13 00:00:00'),
(1127, 58, 17, 0, '2025-05-13 00:00:00'),
(1128, 58, 18, 0, '2025-05-13 00:00:00'),
(1129, 58, 19, 0, '2025-05-13 00:00:00'),
(1130, 58, 23, 0, '2025-05-13 00:00:00'),
(1131, 58, 24, 0, '2025-05-13 00:00:00'),
(1132, 58, 54, 0, '2025-05-13 00:00:00'),
(1133, 58, 8, 0, '2025-05-13 00:00:00'),
(1134, 58, 22, 0, '2025-05-13 00:00:00'),
(1135, 58, 27, 0, '2025-05-13 00:00:00'),
(1136, 58, 28, 0, '2025-05-13 00:00:00'),
(1137, 58, 29, 0, '2025-05-13 00:00:00'),
(1138, 58, 30, 0, '2025-05-13 00:00:00'),
(1139, 58, 31, 0, '2025-05-13 00:00:00'),
(1140, 58, 33, 0, '2025-05-13 00:00:00'),
(1141, 58, 34, 0, '2025-05-13 00:00:00'),
(1142, 58, 36, 0, '2025-05-13 00:00:00'),
(1143, 58, 42, 0, '2025-05-13 00:00:00'),
(1144, 58, 56, 0, '2025-05-13 00:00:00'),
(1145, 58, 1, 0, '2025-05-13 00:00:00'),
(1146, 58, 2, 0, '2025-05-13 00:00:00'),
(1147, 58, 10, 0, '2025-05-13 00:00:00'),
(1148, 58, 3, 0, '2025-05-13 00:00:00'),
(1149, 58, 4, 0, '2025-05-13 00:00:00'),
(1150, 58, 5, 0, '2025-05-13 00:00:00'),
(1151, 58, 7, 0, '2025-05-13 00:00:00'),
(1152, 58, 12, 0, '2025-05-13 00:00:00'),
(1153, 58, 15, 0, '2025-05-13 00:00:00'),
(1154, 58, 43, 0, '2025-05-13 00:00:00'),
(1155, 58, 44, 0, '2025-05-13 00:00:00'),
(1156, 58, 45, 0, '2025-05-13 00:00:00'),
(1157, 58, 46, 0, '2025-05-13 00:00:00'),
(1158, 58, 47, 0, '2025-05-13 00:00:00'),
(1159, 58, 48, 0, '2025-05-13 00:00:00'),
(1160, 58, 49, 0, '2025-05-13 00:00:00'),
(1161, 58, 50, 0, '2025-05-13 00:00:00'),
(1162, 58, 51, 0, '2025-05-13 00:00:00'),
(1163, 58, 52, 0, '2025-05-13 00:00:00'),
(1164, 58, 53, 0, '2025-05-13 00:00:00'),
(1165, 58, 6, 0, '2025-05-13 00:00:00'),
(1166, 58, 11, 0, '2025-05-13 00:00:00'),
(1167, 58, 14, 0, '2025-05-13 00:00:00'),
(1168, 58, 13, 0, '2025-05-13 00:00:00'),
(1169, 58, 20, 0, '2025-05-13 00:00:00'),
(1170, 58, 21, 0, '2025-05-13 00:00:00'),
(1171, 58, 32, 0, '2025-05-13 00:00:00'),
(1172, 58, 37, 0, '2025-05-13 00:00:00'),
(1173, 58, 38, 0, '2025-05-13 00:00:00'),
(1174, 58, 39, 0, '2025-05-13 00:00:00'),
(1175, 58, 40, 0, '2025-05-13 00:00:00'),
(1176, 58, 41, 0, '2025-05-13 00:00:00'),
(1177, 58, 25, 0, '2025-05-13 00:00:00'),
(1178, 58, 26, 0, '2025-05-13 00:00:00'),
(1179, 58, 35, 0, '2025-05-13 00:00:00'),
(1180, 58, 55, 0, '2025-05-13 00:00:00'),
(1181, 58, 9, 0, '2025-05-13 00:00:00'),
(1182, 58, 16, 0, '2025-05-13 00:00:00'),
(1183, 58, 17, 0, '2025-05-13 00:00:00'),
(1184, 58, 18, 1, '2025-05-13 00:00:00'),
(1185, 58, 19, 0, '2025-05-13 00:00:00'),
(1186, 58, 23, 0, '2025-05-13 00:00:00'),
(1187, 58, 24, 0, '2025-05-13 00:00:00'),
(1188, 58, 54, 0, '2025-05-13 00:00:00'),
(1189, 58, 8, 0, '2025-05-13 00:00:00'),
(1190, 58, 22, 0, '2025-05-13 00:00:00'),
(1191, 58, 27, 0, '2025-05-13 00:00:00'),
(1192, 58, 28, 0, '2025-05-13 00:00:00'),
(1193, 58, 29, 0, '2025-05-13 00:00:00'),
(1194, 58, 30, 0, '2025-05-13 00:00:00'),
(1195, 58, 31, 0, '2025-05-13 00:00:00'),
(1196, 58, 33, 0, '2025-05-13 00:00:00'),
(1197, 58, 34, 0, '2025-05-13 00:00:00'),
(1198, 58, 36, 0, '2025-05-13 00:00:00'),
(1199, 58, 42, 0, '2025-05-13 00:00:00'),
(1200, 58, 56, 0, '2025-05-13 00:00:00'),
(1201, 58, 1, 0, '2025-05-13 00:00:00'),
(1202, 58, 2, 0, '2025-05-13 00:00:00'),
(1203, 58, 10, 0, '2025-05-13 00:00:00'),
(1204, 58, 3, 0, '2025-05-13 00:00:00'),
(1205, 58, 4, 0, '2025-05-13 00:00:00'),
(1206, 58, 5, 0, '2025-05-13 00:00:00'),
(1207, 58, 7, 0, '2025-05-13 00:00:00'),
(1208, 58, 12, 0, '2025-05-13 00:00:00'),
(1209, 58, 15, 0, '2025-05-13 00:00:00'),
(1210, 58, 43, 0, '2025-05-13 00:00:00'),
(1211, 58, 44, 0, '2025-05-13 00:00:00'),
(1212, 58, 45, 0, '2025-05-13 00:00:00'),
(1213, 58, 46, 0, '2025-05-13 00:00:00'),
(1214, 58, 47, 0, '2025-05-13 00:00:00'),
(1215, 58, 48, 0, '2025-05-13 00:00:00'),
(1216, 58, 49, 0, '2025-05-13 00:00:00'),
(1217, 58, 50, 0, '2025-05-13 00:00:00'),
(1218, 58, 51, 0, '2025-05-13 00:00:00'),
(1219, 58, 52, 0, '2025-05-13 00:00:00'),
(1220, 58, 53, 0, '2025-05-13 00:00:00'),
(1221, 58, 6, 0, '2025-05-13 00:00:00'),
(1222, 58, 11, 0, '2025-05-13 00:00:00'),
(1223, 58, 14, 0, '2025-05-13 00:00:00'),
(1224, 58, 13, 0, '2025-05-13 00:00:00'),
(1225, 58, 20, 0, '2025-05-13 00:00:00'),
(1226, 58, 21, 0, '2025-05-13 00:00:00'),
(1227, 58, 32, 0, '2025-05-13 00:00:00'),
(1228, 58, 37, 0, '2025-05-13 00:00:00'),
(1229, 58, 38, 0, '2025-05-13 00:00:00'),
(1230, 58, 39, 0, '2025-05-13 00:00:00'),
(1231, 58, 40, 0, '2025-05-13 00:00:00'),
(1232, 58, 41, 0, '2025-05-13 00:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `friends`
--

CREATE TABLE `friends` (
  `user_id` int(11) NOT NULL,
  `friend_id` int(11) NOT NULL,
  `status` enum('pending','accepted','blocked') DEFAULT 'pending',
  `requester_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `friends`
--

INSERT INTO `friends` (`user_id`, `friend_id`, `status`, `requester_id`, `created_at`) VALUES
(58, 60, 'accepted', 60, '2024-09-30 18:01:11'),
(58, 61, 'accepted', 58, '2024-09-30 17:41:17'),
(60, 58, 'accepted', 60, '2024-09-30 17:38:50'),
(61, 58, 'accepted', 58, '2024-09-30 17:41:36');

-- --------------------------------------------------------

--
-- Table structure for table `nutrition`
--

CREATE TABLE `nutrition` (
  `id` int(11) NOT NULL,
  `nutrition` varchar(255) DEFAULT NULL,
  `type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `calory` decimal(10,2) DEFAULT NULL,
  `carbohydrate` decimal(10,2) DEFAULT NULL,
  `fat` decimal(10,2) DEFAULT NULL,
  `sugar` decimal(10,2) DEFAULT NULL,
  `protein` decimal(10,2) DEFAULT NULL,
  `count` varchar(255) DEFAULT NULL,
  `filter` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `nutrition`
--

INSERT INTO `nutrition` (`id`, `nutrition`, `type`, `calory`, `carbohydrate`, `fat`, `sugar`, `protein`, `count`, `filter`) VALUES
(1, 'chicken', 'H', 286.00, 0.10, 16.90, 0.00, 31.20, 'slice', 4),
(2, 'meat', 'H', 192.00, 0.00, 10.20, 0.00, 23.80, 'slice', 4),
(3, 'cucumber', 'H', 30.00, 7.30, 0.20, 3.40, 1.30, 'piece', 5),
(4, 'banana', 'H', 106.80, 27.41, 0.40, 14.68, 1.31, 'piece', 5),
(5, 'apple', 'H', 95.00, 25.00, 0.30, 19.00, 0.50, 'piece', 5),
(6, 'walnut', 'H', 26.00, 0.60, 2.60, 0.10, 0.60, 'grain', 6),
(7, 'orange', 'H', 70.50, 18.00, 0.20, 12.30, 1.30, 'piece', 5),
(8, 'boiled egg', 'H', 78.00, 0.60, 5.30, 0.60, 6.30, 'piece', 3),
(9, 'zucchini soup', 'H', 132.00, 8.30, 11.20, 5.40, 3.10, 'soup bowl ', 2),
(10, 'fish', 'H', 218.00, 0.00, 4.50, 0.00, 44.00, 'slice', 4),
(11, 'dates', 'H', 22.56, 6.00, 0.03, 5.07, 0.20, 'grain', 6),
(12, 'tomato', 'H', 22.00, 4.80, 0.30, 3.20, 1.10, 'piece', 5),
(13, 'garlic', 'H', 4.50, 1.20, 0.03, 0.05, 0.33, 'grain', 7),
(14, 'olive', 'H', 4.10, 0.12, 0.44, 0.00, 0.03, 'grain', 6),
(15, 'strawberry', 'H', 3.80, 0.90, 0.00, 0.60, 1.00, 'grain', 5),
(16, 'lentil soup', 'H', 139.00, 20.00, 2.80, 0.00, 9.30, 'soup bowl', 2),
(17, 'chickpea soup', 'H', 211.00, 24.00, 10.00, 4.90, 7.90, 'soup bowl', 2),
(18, 'bean soup', 'H', 114.00, 19.00, 1.60, 3.10, 6.00, 'soup bowl', 2),
(19, 'potato soup', 'H', 150.50, 35.18, 0.09, 7.35, 2.80, 'soup bowl', 2),
(20, 'vegitables salad', 'H', 114.00, 11.00, 6.80, 5.30, 3.70, 'small bowl', 7),
(21, 'yogurt', 'H', 126.00, 14.00, 3.20, 14.00, 2.60, 'small bowl', 7),
(22, 'biscuit', 'H', 212.00, 27.00, 9.80, 0.30, 4.20, 'piece', 3),
(23, 'okra soup', 'H', 102.00, 10.10, 5.20, 5.30, 5.60, 'soup bowl', 2),
(24, 'spinach soup', 'H', 201.50, 16.00, 12.00, 2.80, 7.50, 'soup bowl', 2),
(25, 'milk', 'H', 122.00, 12.00, 4.80, 12.00, 8.10, 'cup', 1),
(26, 'tea', 'H', 2.40, 0.70, 0.00, 4.20, 0.00, 'cup', 1),
(27, 'rice', 'H', 328.00, 72.00, 0.70, 0.10, 6.90, 'dish', 3),
(28, 'bulgur', 'H', 211.00, 47.60, 0.60, 0.30, 7.80, 'dish', 3),
(29, 'barley bread', 'H', 353.00, 68.36, 4.10, 3.86, 10.58, 'kurdish size', 3),
(30, 'wheat bread', 'H', 312.00, 59.38, 3.12, 3.12, 9.38, 'kurdish size', 3),
(31, 'omlette', 'H', 96.90, 0.40, 7.50, 0.20, 6.30, 'one egg', 3),
(32, 'chips', 'N', 1064.00, 108.00, 68.00, 0.70, 12.70, 'bag', 8),
(33, 'pizza', 'N', 958.00, 120.00, 35.00, 13.00, 41.00, 'medium size', 3),
(34, 'burger', 'N', 540.00, 40.00, 27.00, 0.00, 34.00, 'single patty', 3),
(35, 'soft drinking', 'N', 155.00, 38.00, 0.90, 37.00, 0.00, 'can', 1),
(36, 'french fries', 'N', 365.00, 48.00, 17.00, 0.40, 4.00, 'medium serving', 3),
(37, 'running', 'H', 13.58, 0.00, 0.00, 0.00, 0.00, 'minute', 10),
(38, 'swimming', 'H', 8.33, 0.00, 0.00, 0.00, 0.00, 'minute', 10),
(39, 'walking', 'H', 4.58, 0.00, 0.00, 0.00, 0.00, 'minute', 10),
(40, 'focusing', 'H', 1.67, 0.00, 0.00, 0.00, 0.00, 'minute', 10),
(41, 'sleep', 'H', 60.00, 0.00, 0.00, 0.00, 0.00, 'hour', 10),
(42, 'cake', 'N', 262.00, 38.00, 12.00, 28.00, 2.00, 'slice', 3),
(43, 'eggplant', 'H', 198.00, 49.00, 1.30, 18.00, 4.70, 'piece', 5),
(44, 'kiwi', 'H', 42.00, 10.00, 0.40, 6.20, 0.80, 'piece', 5),
(45, 'watermelon', 'H', 86.00, 22.00, 0.40, 18.00, 1.70, 'slice', 5),
(46, 'melon', 'H', 106.00, 25.00, 0.60, 24.00, 2.60, 'slice', 5),
(47, 'grape', 'H', 3.40, 0.90, 0.00, 0.80, 0.00, 'grain', 5),
(48, 'peach', 'H', 59.00, 14.00, 0.40, 13.00, 1.40, 'piece', 5),
(49, 'pear', 'H', 101.00, 27.00, 0.30, 17.00, 0.60, 'piece', 5),
(50, 'pomegranate', 'H', 234.00, 53.00, 3.30, 39.00, 4.70, 'piece', 5),
(51, 'pinapple', 'H', 41.00, 11.00, 0.10, 8.30, 0.50, 'slice', 5),
(52, 'lemon', 'H', 24.00, 7.80, 0.30, 2.10, 0.90, 'piece', 5),
(53, 'raisins', 'H', 64.50, 17.00, 0.14, 12.50, 0.70, 'fist', 5),
(54, 'apricot soup', 'H', 177.00, 40.40, 0.25, 33.50, 1.50, 'soup bowl', 2),
(55, 'water', 'H', 7.50, 0.00, 0.00, 0.00, 0.00, 'glass', 1),
(56, 'cheese', 'H', 113.00, 0.90, 9.30, 0.10, 6.40, 'slice', 3);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(320) NOT NULL,
  `role` varchar(20) NOT NULL DEFAULT 'user',
  `gender` varchar(6) NOT NULL,
  `birth_date` date NOT NULL,
  `weight` int(3) NOT NULL,
  `height` int(3) NOT NULL,
  `medical_condition` varchar(200) NOT NULL,
  `bmi` decimal(5,2) DEFAULT NULL,
  `app_lang` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'ckb',
  `account_number` varchar(255) DEFAULT NULL,
  `create_datetime` datetime NOT NULL,
  `remember_me_token` varchar(255) DEFAULT NULL,
  `reset_token` varchar(100) DEFAULT NULL,
  `token_expiry` int(11) DEFAULT NULL,
  `push_notifications_enabled` tinyint(1) DEFAULT 1,
  `premium` int(11) DEFAULT 2
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `username`, `full_name`, `password`, `email`, `role`, `gender`, `birth_date`, `weight`, `height`, `medical_condition`, `bmi`, `app_lang`, `account_number`, `create_datetime`, `remember_me_token`, `reset_token`, `token_expiry`, `push_notifications_enabled`, `premium`) VALUES
(58, 'M-TAG96', 'Mohammed Taifur AbdulGhafur', '$2y$10$zQkyAJpETm8EZJRcH5VXouOucL0Cn5XnRp5bhaSG9PL.BTqCh5V66', '<EMAIL>', 'user', 'male', '1996-02-17', 89, 176, 'Cardiovascular Disease, Diabetes, Hypertension, Overweight', 28.73, 'en', 'ACC-66fa5878f347c4.********', '2024-09-30 09:51:20', '11b2fad252c4acdd34a60e85e03f85d4', 'c8c6f19206a98632964d8dd4edad5491d76a3c091e5b7ac4e7f6ecae1651982e0f5682afd5bed37ab1d28648908790a4e50f', **********, 1, 2),
(60, 'Mohammed', 'Mohammed Taifur AGhafur', '$2y$10$3IRwC9j.NbmumYWic8ffy.0mwGwzMPzcHGGp18uWpe91pQiw/V/xq', '<EMAIL>', 'user', 'male', '2000-01-01', 70, 180, 'Cardiovascular Disease, Diabetes, Hypertension', 22.00, 'en', 'ACC-66fa5b88190191.********', '2024-09-30 10:04:24', '95ec57906b9a9711fdac33a8bdd63e4d', NULL, NULL, 1, 2),
(61, 'SavaAzad', 'Sava Azad Karoomi', '$2y$10$ZuapUfcWr9UiAmrJR3kj6OKIFuLx4/PQ2k3xttw2KE7tpg8YCbOgO', '<EMAIL>', 'user', 'female', '1996-05-28', 80, 160, 'Obesity', 31.00, 'en', 'ACC-66fa6549a521e7.39129811', '2024-09-30 10:46:01', NULL, NULL, NULL, 1, 2),
(63, 'Haiv.magazine', 'Mohammed Taifur AbdulGhafur', '$2y$10$SqS2VVhG6wMBm7jxUqQ30.mhB1cis1GsCVbEaW62Xuo8TjpXD8oay', '<EMAIL>', 'user', 'male', '1996-12-12', 123, 200, 'Obesity', 30.75, 'tr', 'ACC-67017a387abe86.87259174', '2024-10-05 19:41:12', NULL, NULL, NULL, 1, 2);

-- --------------------------------------------------------

--
-- Table structure for table `user_nutrition`
--

CREATE TABLE `user_nutrition` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `total_calory` decimal(10,2) NOT NULL,
  `total_carbohydrate` decimal(10,2) NOT NULL,
  `total_fat` decimal(10,2) NOT NULL,
  `total_sugar` decimal(10,2) NOT NULL,
  `total_protein` decimal(10,2) NOT NULL,
  `health_status` int(3) NOT NULL,
  `diabetes` int(3) NOT NULL,
  `bp_h` int(3) NOT NULL,
  `bp_l` int(3) NOT NULL,
  `input_date` datetime DEFAULT '2024-02-29 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_nutrition`
--

INSERT INTO `user_nutrition` (`id`, `user_id`, `total_calory`, `total_carbohydrate`, `total_fat`, `total_sugar`, `total_protein`, `health_status`, `diabetes`, `bp_h`, `bp_l`, `input_date`) VALUES
(1, 58, 1035.99, 678.71, 242.71, 70.31, 182.07, 69, 0, 0, 0, '2024-09-30 00:00:00'),
(2, 60, 2744.00, 392.00, 159.90, 79.10, 93.70, 51, 0, 0, 0, '2024-09-30 00:00:00'),
(3, 61, 812.42, 173.00, 38.30, 52.00, 45.70, 0, 0, 0, 0, '2024-09-30 00:00:00'),
(5, 60, 610.00, 60.00, 24.00, 60.00, 40.50, 31, 0, 0, 0, '2024-10-03 00:00:00'),
(6, 58, 184.24, 150.26, 3.91, 23.68, 16.88, 12, 0, 0, 0, '2024-10-05 00:00:00'),
(7, 58, 78.90, 19.70, 1.60, 7.30, 6.00, 5, 0, 0, 0, '2025-05-13 00:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `user_tokens`
--

CREATE TABLE `user_tokens` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `selector` varchar(12) NOT NULL,
  `token_hash` varchar(64) NOT NULL,
  `expires` datetime NOT NULL,
  `device_info` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_tokens`
--

INSERT INTO `user_tokens` (`id`, `user_id`, `selector`, `token_hash`, `expires`, `device_info`) VALUES
(1, 58, '0834eb9b9952', '32966cf6d35c195097e0d5883f9cc8bd5358442d4b075fe01c64333ff578b534', '2025-02-20 15:17:03', NULL),
(2, 58, '6386e166e30a', 'eaf4215df84ff42f64191a439b1afa24a06514c8fd9956d389dfec6f719b6b52', '2025-06-11 15:20:15', NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `food_consumption`
--
ALTER TABLE `food_consumption`
  ADD PRIMARY KEY (`consumption_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `food_id` (`food_id`);

--
-- Indexes for table `friends`
--
ALTER TABLE `friends`
  ADD PRIMARY KEY (`user_id`,`friend_id`),
  ADD KEY `friend_id` (`friend_id`),
  ADD KEY `requester_id` (`requester_id`);

--
-- Indexes for table `nutrition`
--
ALTER TABLE `nutrition`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `account_number` (`account_number`);

--
-- Indexes for table `user_nutrition`
--
ALTER TABLE `user_nutrition`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `user_tokens`
--
ALTER TABLE `user_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `selector` (`selector`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `food_consumption`
--
ALTER TABLE `food_consumption`
  MODIFY `consumption_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1233;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=64;

--
-- AUTO_INCREMENT for table `user_nutrition`
--
ALTER TABLE `user_nutrition`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `user_tokens`
--
ALTER TABLE `user_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `food_consumption`
--
ALTER TABLE `food_consumption`
  ADD CONSTRAINT `food_consumption_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `food_consumption_ibfk_2` FOREIGN KEY (`food_id`) REFERENCES `nutrition` (`id`);

--
-- Constraints for table `friends`
--
ALTER TABLE `friends`
  ADD CONSTRAINT `friends_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `friends_ibfk_2` FOREIGN KEY (`friend_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `friends_ibfk_3` FOREIGN KEY (`requester_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `user_tokens`
--
ALTER TABLE `user_tokens`
  ADD CONSTRAINT `user_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

<?php
// Include configuration file if not already included
if (!defined('BASE_URL')) {
    require_once __DIR__ . '/../includes/config.php';
}

// Set default direction (left to right)
$dir = 'ltr'; // Default direction (left to right)

// Check if user is signed in and get their language preference from the database
if (isset($_SESSION['user_id'])) {
    // Assuming 'user_id' is the session variable storing the user's ID after login
    $userId = $_SESSION['user_id'];

    // Fetch user's language preference from the database
    $query = "SELECT app_lang FROM users WHERE user_id = '$userId'";
    $result = mysqli_query($con, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $lang = $row['app_lang'];

        // Set direction based on language
        if ($lang === 'ckb' || $lang === 'ar' || $lang === 'fa') {
            $dir = 'rtl';
        }
    } else {
        // User not found or language preference not set, fallback to default
        $lang = 'en';
    }
} else {
    // User not signed in, fallback to default
    $lang = 'en';
}

// Function to dynamically set direction attribute based on user preferences
function getDirAttribute($dir) {
    $dirAttribute = "dir=\"$dir\"";
    return $dirAttribute;
}

$dir = getDirAttribute($dir);
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" <?php echo $dir; ?>>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C-Health</title>
    <link rel="manifest" href="<?php echo url('manifest.php'); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo url('assets/css/style-dynamic.php'); ?>">
    <link rel="icon" type="image/x-icon" href="<?php echo url('assets/img/fav.ico'); ?>">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-1.10.2.js"></script>
    <!-- Bootstrap JS and jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>

<body>
    <div class="upperTab centered position-relative py-2">
        <a href="<?php echo url('dashboard.php'); ?>" class="alogo mx-auto text-center">
            <img src="<?php echo url('assets/img/logo.png'); ?>" class="logo img-fluid" alt="Logo"
                style="height: 50px;"> <!-- Adjust height as needed -->
        </a>
        <div class="position-absolute end-0 mx-2">
            <!-- Include PWA Install Dialog -->
            <?php include __DIR__ . '/pwa-install-dialog.php'; ?>
        </div>
    </div>
    <div class="supportLower"></div>
    <div class="navigation">
        <ul>
            <li class="list active">
                <a class="home" href="#home">
                    <span class="icon">
                        <img class="navIcons" src="<?php echo url('assets/img/home.png'); ?>" alt="Home Icon">
                    </span>
                    <span class="text">Home</span>
                    <span class="circle"></span>
                </a>
            </li>
            <li class="list">
                <a class="statistics" href="#statistics">
                    <span class="icon">
                        <img class="navIcons" src="<?php echo url('assets/img/statistics.png'); ?>"
                            alt="Statistics Icon">
                    </span>
                    <span class="text">Statistics</span>
                    <span class="circle"></span>
                </a>
            </li>
            <li class="list">
                <a class="input" href="#input">
                    <span class="icon">
                        <img class="navIcons" src="<?php echo url('assets/img/input.png'); ?>" alt="Input Icon">
                    </span>
                    <span class="text">Input</span>
                    <span class="circle"></span>
                </a>
            </li>
            <li class="list">
                <a class="profile" href="#profile">
                    <span class="icon">
                        <img class="navIcons" src="<?php echo url('assets/img/profile.png'); ?>" alt="Profile Icon">
                    </span>
                    <span class="text">Profile</span>
                    <span class="circle"></span>
                </a>
            </li>
            <li class="list">
                <a class="guide" href="#guide">
                    <span class="icon">
                        <img class="navIcons" src="<?php echo url('assets/img/guide.png'); ?>" alt="Guide Icon">
                    </span>
                    <span class="text">Guide</span>
                    <span class="circle"></span>
                </a>
            </li>
            <div class="indicator"></div>
        </ul>
    </div>

    <script>
    // Make sure the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Get all navigation list items
        const list = document.querySelectorAll(".list");

        // Function to handle active link
        function activeLink() {
            list.forEach((item) => item.classList.remove("active"));
            this.classList.add("active");
        }

        // Add click event listener to each list item
        list.forEach((item) => item.addEventListener("click", activeLink));

        // Set initial active state based on URL hash
        const hash = window.location.hash || '#home';
        const targetItem = document.querySelector(`a[href="${hash}"]`);
        if (targetItem) {
            const parentLi = targetItem.closest('li');
            if (parentLi) {
                list.forEach((item) => item.classList.remove("active"));
                parentLi.classList.add("active");
            }
        }
    });
    </script>
</body>

</html>
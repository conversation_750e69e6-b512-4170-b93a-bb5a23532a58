<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/db.php';
include('components/translate_alert.php');


$language = isset($_GET['lang']) ? $_GET['lang'] : 'en'; // Default to 'en' if no language parameter is provided

if (isset($_POST['email'])) {
    $email = mysqli_real_escape_string($con, $_POST['email']);
    $query = "SELECT * FROM `users` WHERE email='$email'";
    $result = mysqli_query($con, $query) or die(mysqli_error($con));
    if (mysqli_num_rows($result) == 1) {
        $token = bin2hex(random_bytes(50));
        $expiry = time() + 3600; // 1 hour expiry
        $update_query = "UPDATE `users` SET reset_token='$token', token_expiry='$expiry' WHERE email='$email'";
        mysqli_query($con, $update_query) or die(mysqli_error($con));


        // Use the BASE_URL constant to create the reset link
        $reset_link = BASE_URL . "/reset_password.php?token=$token&lang=$language";
        $subject = "Password Recovery";
        $message = "Click the following link to reset your password: $reset_link";
        $headers = "From: <EMAIL>";

        if (mail($email, $subject, $message, $headers)) {
            echo "<script> showTranslatedAlert('A password recovery email has been sent.');  window.location.href = '" . url('login.php') . "';</script>";
            exit;
        } else {
            echo "<script> showTranslatedAlert('Failed to send recovery email.')</script>";
        }
    } else {
        echo "<script> showTranslatedAlert('No user found with that email address.')</script>";
    }
}
?>
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password</title>
    <link rel="icon" type="image/x-icon" href="<?php echo url('assets/img/fav.ico'); ?>">
    <link rel="manifest" href="<?php echo url('manifest.php'); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo url('assets/css/style-dynamic.php'); ?>"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
</head>
<body>

<?php
    // Load the translations from the JSON file
    $translations = json_decode(file_get_contents(__DIR__ . '/assets/lang/placeholders.json'), true);

    // Get the desired language from the URL parameter
    $language = isset($_GET['lang']) ? $_GET['lang'] : 'en'; // Default to 'en' if no language parameter is provided

    // Function to get the translation for a given placeholder and language
    function translate($placeholder, $language, $translations) {
        return isset($translations['placeholders'][$placeholder][$language]) ? $translations['placeholders'][$placeholder][$language] : $translations['placeholders'][$placeholder]['en'];
    }
?>

<?php include("components/language_selection.php"); ?>

<form class="form p-3" method="post" action="<?php echo url('forgot_password.php?lang=' . $language); ?>">
    <center><img src="<?php echo url('assets/img/logo.png'); ?>" alt="logo" width="100%"></center>
    <h1 class="login-title">Forgot Password</h1>
    <input type="email" class="form-control my-3 p-2 rounded-pill p-2" id="email" name="email" placeholder="<?php echo translate('Email Address', $language, $translations); ?>" required>
    <button type="submit" class="form-control btn btn-success p-2 rounded-pill cButtonFilling">Submit</button>
</form>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        const langSelect = document.getElementById('lang-select');
        const urlParams = new URLSearchParams(window.location.search);
        const currentLang = urlParams.get('lang') || 'en'; // Default to English if no language is selected

        // Set the selected value of the dropdown based on the URL parameter
        langSelect.value = currentLang;

        // Update HTML attributes based on the selected language
        updateHtmlAttributes(currentLang);

        // Load the JSON file and update the text on the page
        loadJSON(currentLang);

        langSelect.addEventListener('change', function() {
            const selectedLang = this.value;
            urlParams.set('lang', selectedLang);
            window.location.search = urlParams.toString();
        });
    });

    function updateHtmlAttributes(lang) {
        const langDir = {
            'ckb': 'rtl',
            'kmr': 'ltr',
            'en': 'ltr',
            'ar': 'rtl',
            'fa': 'rtl',
            'tu': 'ltr'
        };

        document.documentElement.lang = lang;
        document.documentElement.dir = langDir[lang] || 'ltr';
    }

    function loadJSON(lang) {
        var baseUrl = '<?php echo BASE_URL; ?>';
        var filePath = baseUrl + '/assets/lang/' + lang + '.json';

        fetch(filePath)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                var walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                var node;
                while ((node = walker.nextNode()) !== null) {
                    var text = node.nodeValue.trim();
                    if (text !== "") {
                        if (data[text]) {
                            node.nodeValue = data[text];
                        }
                    }
                }
            })
            .catch(error => console.error('Error loading translation:', error));
    }
</script>

<div class="appVersion position-absolute top-0 start-0 px-2 p-2 text-muted">
    <?php include(__DIR__ . "/components/app_version.php"); ?>
</div>

<script>
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
      var baseUrl = '<?php echo BASE_URL; ?>';
      navigator.serviceWorker.register(baseUrl + '/sw-new.js?v=1.0', {
        scope: baseUrl + '/'
      }).then(function(registration) {
        console.log('ServiceWorker registration successful with scope: ', registration.scope);
      }, function(err) {
        console.log('ServiceWorker registration failed: ', err);
      });
    });
  }
</script>

</body>
</html>

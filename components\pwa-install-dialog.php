<?php
// PWA Install Dialog Component with <PERSON>ert Confirmation and iOS Support

// Include configuration file if not already included
if (!defined('BASE_URL')) {
    require_once __DIR__ . '/../includes/config.php';
}
?>
<script>
    let deferredPrompt;

    // Function to check if the user is on iOS
    function isIOS() {
        return /iPhone|iPad|iPod/.test(navigator.userAgent) && !window.MSStream;
    }

    // Function to handle the install button click
    function confirmInstall() {
        if (isIOS()) {
            // Show instructions for iOS users
            alert("To install this app, open it in Safari, tap the 'Share' button, and then 'Add to Home Screen'.");
        } else if (deferredPrompt) {
            // For non-iOS devices, show the PWA installation prompt
            if (confirm("Do you want to install this app?")) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        alert('App installed successfully!');
                        console.log('User accepted the installation');
                    } else {
                        console.log('User dismissed the installation');
                    }
                    deferredPrompt = null; // Reset the event
                });
            } else {
                console.log('Installation canceled.');
            }
        }
    }

    // Listen for the 'beforeinstallprompt' event for non-iOS devices
    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault(); // Prevent the default browser prompt
        deferredPrompt = e; // Store the event for later use

        if (!isIOS()) {
            // Show the install button for non-iOS devices
            document.getElementById('install-button').style.display = 'block';
        }
    });

    // Always show the install button for iOS devices
    if (isIOS()) {
        document.getElementById('install-button').style.display = 'block';
    }

    // Handle the 'appinstalled' event
    window.addEventListener('appinstalled', () => {
        alert('Thank you for installing the app!');
        console.log('PWA installed');
        document.getElementById('install-button').style.display = 'none'; // Hide the install button
    });
</script>

<!-- Install Button -->
<div type="update" id="install-button" class="btn rounded-pill cButtonFill mx-1 p-1" style="display: none; padding: 15px;" onclick="confirmInstall()">
    <img src="<?php echo url('assets/img/fav.ico'); ?>" alt="App Icon" class="mx-1" style="width: 20px; height: 20px;">
    Install App
</div>

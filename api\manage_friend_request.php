<?php
session_start();
require '../includes/db.php';

// Ensure the user is logged in
if (!isset($_SESSION['user_id'])) {
    echo 'You need to log in first.';
    exit();
}

$current_user_id = $_SESSION['user_id'];

if (isset($_POST['requester_id'], $_POST['action'])) {
    $requester_id = intval($_POST['requester_id']);
    $action = $_POST['action'];

    if ($action == 'accept') {
        // Update the status to 'accepted' for the original request
        $update_stmt = $con->prepare("UPDATE friends SET status = 'accepted' WHERE user_id = ? AND friend_id = ?");
        $update_stmt->bind_param("ii", $requester_id, $current_user_id);
        $update_stmt->execute();
        $update_stmt->close();

        // Insert reciprocal friendship
        $insert_stmt = $con->prepare("INSERT INTO friends (user_id, friend_id, status, requester_id) VALUES (?, ?, 'accepted', ?)");
        $insert_stmt->bind_param("iii", $current_user_id, $requester_id, $requester_id);
        $insert_stmt->execute();
        $insert_stmt->close();

        echo 'Friend request accepted.';
    } elseif ($action == 'reject') {
        // Delete the friend request
        $delete_stmt = $con->prepare("DELETE FROM friends WHERE user_id = ? AND friend_id = ?");
        $delete_stmt->bind_param("ii", $requester_id, $current_user_id);
        $delete_stmt->execute();
        $delete_stmt->close();

        echo 'Friend request rejected.';
    } else {
        echo 'Invalid action.';
    }
} else {
    echo 'Invalid request.';
}
?>

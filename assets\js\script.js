//NAV   START
document.addEventListener("DOMContentLoaded", function () {
  const list = document.querySelectorAll(".list");
  function activeLink() {
    list.forEach((item) => item.classList.remove("active"));
    this.classList.add("active");
  }
  list.forEach((item) => item.addEventListener("click", activeLink));

  // Set initial active state based on URL hash
  const hash = window.location.hash || "#home";
  const targetItem = document.querySelector(`a[href="${hash}"]`);
  if (targetItem) {
    const parentLi = targetItem.closest("li");
    if (parentLi) {
      list.forEach((item) => item.classList.remove("active"));
      parentLi.classList.add("active");
    }
  }
});

$(".home, .statistics, .input, .profile, .guide").click(function () {
  var clickedClass = $(this).attr("class");
  var targetId = "#" + clickedClass;

  $(targetId).css({
    "z-index": "9",
    display: "block",
  });

  $(".home, .statistics, .input, .profile, .guide")
    .not(this)
    .each(function () {
      var otherClass = $(this).attr("class");
      $("#" + otherClass).css({
        "z-index": "5",
        display: "none",
      });
    });
});
// ------------------------
// Get the current URL
var currentUrl = window.location.href;

// Check if the URL contains specific keywords and update z-index accordingly
if (currentUrl.includes("/dashboard.php")) {
  document.querySelector("#home").style.zIndex = 9;
  document.querySelector("#home").style.display = "block";
} else if (currentUrl.includes("/dashboard.php#home")) {
  document.querySelector("#home").style.zIndex = 9;
  document.querySelector("#home").style.display = "block";
} else if (currentUrl.includes("/dashboard.php#statistics")) {
  document.querySelector("#statistics").style.zIndex = 9;
  document.querySelector("#statistics").style.display = "block";
} else if (currentUrl.includes("/dashboard.php#input")) {
  document.querySelector("#input").style.zIndex = 9;
  document.querySelector("#input").style.display = "block";
} else if (currentUrl.includes("/dashboard.php#profile")) {
  document.querySelector("#profile").style.zIndex = 9;
  document.querySelector("#profile").style.display = "block";
} else if (currentUrl.includes("/dashboard.php#guide")) {
  document.querySelector("#guide").style.zIndex = 9;
  document.querySelector("#guide").style.display = "block";
}

$(document).ready(function () {
  // Function to enable or disable scrolling based on the target class
  function toggleScrolling(enableScrolling) {
    $("body").css("overflow", enableScrolling ? "auto" : "hidden");
  }

  // Attach click event listeners to the anchor tags
  $(".input, .guide").click(function () {
    toggleScrolling(true); // Enable scrolling
  });

  $(".home, .statistics, .profile").click(function () {
    toggleScrolling(false); // Disable scrolling
  });
});

//NAV   END

//TRANSLATION   START

// Function to load JSON file dynamically
function loadJSON(lang) {
  // Get the base URL from the current path
  var basePath = "";
  var pathSegments = window.location.pathname.split("/");
  if (pathSegments.length > 1 && pathSegments[1] === "c-health") {
    basePath = "/c-health";
  }

  var filePath = basePath + "/assets/lang/" + lang + ".json";

  fetch(filePath)
    .then((response) => {
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      return response.json();
    })
    .then((data) => {
      var walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );

      var node;
      while ((node = walker.nextNode()) !== null) {
        var text = node.nodeValue.trim();
        if (text !== "") {
          if (data[text]) {
            node.nodeValue = data[text];
          }
        }
      }
    })
    .catch((error) => console.error("Error loading translation:", error));
}

// Get the lang attribute value
var lang = document.documentElement.lang;

// Load JSON file based on lang attribute value
loadJSON(lang);

//TRANSLATION   END

//PWA Service Worker

if ("serviceWorker" in navigator) {
  window.addEventListener("load", function () {
    // Get the base URL from the current path
    var basePath = "";
    var pathSegments = window.location.pathname.split("/");
    if (pathSegments.length > 1 && pathSegments[1] === "c-health") {
      basePath = "/c-health";
    }

    navigator.serviceWorker
      .register(basePath + "/sw.js", {
        scope: basePath + "/",
      })
      .then(
        function (registration) {
          console.log(
            "ServiceWorker registration successful with scope: ",
            registration.scope
          );
        },
        function (err) {
          console.log("ServiceWorker registration failed: ", err);
        }
      );
  });
}

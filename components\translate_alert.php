<?php
// translate_alert.php

// Determine the current language (default to 'en' if not set)
$app_lang = isset($_GET['lang']) ? $_GET['lang'] : 'en';
$app_lang = preg_replace('/[^a-zA-Z0-9_-]/', '', $app_lang); // Sanitize input

// Pass the current language to JavaScript
echo "<script>var appLang = '" . htmlspecialchars($app_lang, ENT_QUOTES, 'UTF-8') . "';</script>";

// Include configuration file if not already included
if (!defined('BASE_URL')) {
    require_once __DIR__ . '/../includes/config.php';
}

// Load and parse the translation file or default to English
$lang_path = __DIR__ . '/../assets/lang/' . $app_lang . '.json';
if (file_exists($lang_path)) {
    $translations = json_decode(file_get_contents($lang_path), true);
} else {
    $translations = json_decode(file_get_contents(__DIR__ . '/../assets/lang/en.json'), true);
}

// Pass the static translations to JavaScript (if needed)
echo "<script>var staticTranslations = " . json_encode($translations) . ";</script>";

// Include JavaScript functions for translation and alert
?>
<script>
// translate_alert.php JavaScript functions

var translations = {}; // Will hold the translations loaded via fetch

// Function to load translations dynamically
function loadTranslations(callback) {
    if (Object.keys(translations).length > 0) {
        // Translations already loaded
        if (typeof callback === 'function') callback();
        return;
    }

    var baseUrl = '<?php echo BASE_URL; ?>';
    fetch(baseUrl + '/assets/lang/' + appLang + '.json')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok ' + response.statusText);
            }
            return response.json();
        })
        .then(data => {
            translations = data;
            if (typeof callback === 'function') callback();
        })
        .catch(error => {
            console.error('Error loading translation JSON:', error);
            translations = {}; // Use empty translations on error
            if (typeof callback === 'function') callback();
        });
}

// Function to show translated alert
function showTranslatedAlert(messageKey) {
    if (Object.keys(translations).length === 0) {
        // Load translations if not already loaded
        loadTranslations(function() {
            var message = translations[messageKey] || messageKey;
            alert(message);
        });
    } else {
        var message = translations[messageKey] || messageKey;
        alert(message);
    }
}

// Preload translations on page load (optional)
document.addEventListener('DOMContentLoaded', function() {
    loadTranslations();
});
</script>

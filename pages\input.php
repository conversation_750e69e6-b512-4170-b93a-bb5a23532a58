<?php
    // Fetch the nutrition data from the 'nutrition' table
    $sql = "SELECT * FROM nutrition ORDER BY filter ASC";
    $result = mysqli_query($con, $sql);

    if (!$result) {
        die("Error: " . mysqli_error($con));
    }
?>

<center>
    <h2 style="padding: 25px 0 !important;">Nutrition Input Page</h2>
</center>

<form class="px-4" style="padding-bottom: 50px !important;" action="<?php echo url('api/calculate.php'); ?>" method="post">
    <center>
        <div class="d-none d-sm-block border border-secondary border-1 rounded-pill p-2 w-25">
        <h6 style="font-size: 15px;" class="d-inline"><i style="color: #2e7d32; font-size: 10px;" class='bi bi-check-circle-fill'></i><p class="d-inline"> = </p><p class="d-inline">Healthy</p></h6>
        &nbsp; &#160;
        <h6 style="font-size: 15px;" class="d-inline"><i style="color: crimson; font-size: 10px;" class='bi bi-x-circle-fill'></i><p class="d-inline"> = </p><p class="d-inline">Non-Healthy</p></h6>
        </div>
        <div class="d-block d-sm-none border border-secondary border-1 rounded-pill p-2 w-100">
        <h6 style="font-size: 15px;" class="d-inline"><i style="color: #2e7d32; font-size: 10px;" class='bi bi-check-circle-fill'></i><p class="d-inline"> = </p><p class="d-inline">Healthy</p></h6>
        &nbsp; &#160;
        <h6 style="font-size: 15px;" class="d-inline"><i style="color: crimson; font-size: 10px;" class='bi bi-x-circle-fill'></i><p class="d-inline"> = </p><p class="d-inline">Non-Healthy</p></h6>
        </div>
        <table border="0" style="margin-left: auto; margin-right: auto;">
            <tr>
                <th>Food</th>
                <th>Amount</th>
                <th>&nbsp;Type</th>
            </tr>

            <?php while ($row = mysqli_fetch_assoc($result)) { ?>
                <tr>
                    <td class='py-1 pe-2'>
                        <?php
                        $icon = ''; // Initialize icon variable
                        $color = ''; // Initialize background color variable

                        if ($row['type'] === 'N') {
                            $icon = "<i class='bi bi-x-circle-fill'></i>"; // Bootstrap icon for 'N'
                            $color = 'crimson'; // Set background color to crimson for 'N'
                        } elseif ($row['type'] === 'H') {
                            $icon = "<i class='bi bi-check-circle-fill'></i>"; // Bootstrap icon for 'H'
                            $color = '#2e7d32'; // Set background color to #2e7d32 for 'H'
                        }
                        // Output the colored circle icon, nutrition text, and set background color
                        echo "<p class='nutritionName d-inline mx-1' style='color: $color;'>$icon</p>" . $row['nutrition'];
                        ?>
                    </td>
                    <td><input class='form-control border-dark rounded-pill text-center' style='width: 35px;' type='number' name='amount[<?= $row['id'] ?>]' step='0.25' min='0'></td>
                    <td class='py-1 pe-2'><?= $row['count'] ?></td>
                </tr>
            <?php } ?>


            <?php
                // Retrieve user information from the database
                $query = "SELECT medical_condition FROM users WHERE user_id = ?";
                $stmt = mysqli_prepare($con, $query);

                if (!$stmt) {
                    die("Error: " . mysqli_error($con));
                }

                mysqli_stmt_bind_param($stmt, "i", $user_id);
                mysqli_stmt_execute($stmt);

                $result = mysqli_stmt_get_result($stmt);

                if ($row = mysqli_fetch_assoc($result)) {
                    $medical_conditions = explode(', ', $row['medical_condition']);

                    // Check if any of the medical conditions match the specific cases
                    $display_diabetes = in_array('Diabetes', $medical_conditions);
                    $display_hypertension = in_array('Hypertension', $medical_conditions);
                    $display_cardiovascular = in_array('Cardiovascular Disease', $medical_conditions);

                    // Display additional inputs based on medical conditions
                    if ($display_diabetes) {
                        echo "<tr>";
                        echo "<td class='py-1 pe-2' for='diabetes'>Diabetes</td>";
                        echo "<td><input class='form-control border-dark rounded-pill text-center' style='width:50px;' type='number' name='diabetes' step='1' min='0'></td>";
                        echo "<td> mmol/L</td>";
                        echo "</tr>";
                    }

                    if ($display_hypertension || $display_cardiovascular) {
                        echo "<tr>";
                        echo "<td class='py-1 pe-2' for='bp_h'>Blood Pressure</td>";
                        echo "<td><input class='form-control border-dark rounded-pill text-center' placeholder='&uarr;' style='width:50px;' type='number' name='bp_h' step='1' min='0'></td>";
                        echo "<td><input class='form-control border-dark rounded-pill text-center' placeholder='&darr;' style='width:50px; display: inline;' type='number' name='bp_l' step='1' min='0'> mmHg</td>";
                        echo "</tr>";
                    }
                }

                mysqli_stmt_close($stmt);
            ?>
        </table>
    </center>

    <br>
    <center>
        <button id="calculateButton" class="btn btn-secondary rounded-pill p-2 px-3" type="submit"><i class="bi bi-calculator align-middle"></i><p class="d-inline">&nbsp;</p>Calculate</button>
    </center>
</form>

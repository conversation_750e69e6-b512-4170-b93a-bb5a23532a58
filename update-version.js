const fs = require('fs');
const semver = require('semver');

// Get the commit message file path from the command line arguments
const commitMsgFilePath = process.argv[2];

// Path to manifest.json
const manifestPath = './manifest.json';
const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));

// Get the current version from manifest.json
const currentVersion = manifest.version || '1.0.0';

// Initialize incrementType to 'patch'
let incrementType = 'patch';

try {
  // Read the commit message from the file
  const commitMessage = fs.readFileSync(commitMsgFilePath, 'utf8').trim().toLowerCase();

  console.log('Commit Message:', commitMessage);

  // Determine the version increment type based on keywords
  if (commitMessage.includes('breaking')) {
    incrementType = 'major';
  } else if (commitMessage.includes('feature')) {
    incrementType = 'minor';
  }

  // Increment the version
  const newVersion = semver.inc(currentVersion, incrementType);

  // Log the version update
  console.log(`Updating version from ${currentVersion} to ${newVersion} (Type: ${incrementType})`);

  // Update the version in manifest.json
  manifest.version = newVersion;
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2), 'utf8');
} catch (error) {
  console.error('Error processing the commit message or updating the version:', error);
  process.exit(1); // Exit with error code to prevent commit
}

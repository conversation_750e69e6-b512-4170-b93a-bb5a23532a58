<?php
require 'includes/db.php';
include('components/translate_alert.php');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: login.php');
    exit();
}

// Fetch user details from the database
$username = $_SESSION['username'];
$query = "SELECT user_id, account_number FROM users WHERE username = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("s", $username);
$stmt->execute();
$user_result = $stmt->get_result();

if ($user_result->num_rows > 0) {
    $user = $user_result->fetch_assoc();
    $user_id = $user['user_id'];
    $account_number = $user['account_number'];
} else {
    echo "User not found!";
    exit();
}

// Retrieve user information
$query = "SELECT * FROM users WHERE username = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $full_name = $row['full_name'];
    $username = $row['username'];
    $gender = $row['gender'];
    $birth_date = $row['birth_date'];
    $weight = $row['weight'];
    $height = $row['height'];
    $medical_condition = $row['medical_condition'];
    $app_lang = $row['app_lang'];

    // Calculate age from birthdate
    $birthdate = new DateTime($birth_date);
    $currentDate = new DateTime();
    $age = $currentDate->diff($birthdate)->y;

    // Function to get language name and flag image path
    function getLanguageInfo($app_lang) {
        switch ($app_lang) {
            case 'en':
                return ['English', url('assets/img/en.png')];
            case 'ar':
                return ['Arabic', url('assets/img/ar.png')];
            case 'ckb':
                return ['Kurdish', url('assets/img/ckb.png')];
            case 'kmr':
                return ['Kurdish', url('assets/img/ckb.png')];
            case 'fa':
                return ['Persian', url('assets/img/fa.png')];
            case 'tr':
                return ['Turkish', url('assets/img/tr.png')];
            default:
                return ['Unknown', ''];
        }
    }

    // Get language name and flag image path
    list($language_name, $flag_path) = getLanguageInfo($app_lang);
} else {
    echo "User not found!";
    exit();
}

// Fetch user's friends and their health_status for the current date only
$friend_query = "
    SELECT u.user_id, u.full_name, u.username, u.role, u.bmi, un.health_status
    FROM friends f
    JOIN users u ON f.friend_id = u.user_id
    LEFT JOIN user_nutrition un ON un.user_id = u.user_id AND un.input_date = CURDATE()
    WHERE f.user_id = ? AND f.status = 'accepted'
";
$stmt = $con->prepare($friend_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$friend_result = $stmt->get_result();


// Fetch incoming friend requests
$request_query = "
    SELECT u.user_id, u.username, u.full_name
    FROM friends f
    JOIN users u ON f.user_id = u.user_id
    WHERE f.friend_id = ? AND f.status = 'pending'
";
$stmt = $con->prepare($request_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$request_result = $stmt->get_result();

// Check for pending requests
$has_pending_requests = $request_result->num_rows > 0;
?>


<?php
// Load the translations from the JSON file
$translations = json_decode(file_get_contents('assets/lang/placeholders.json'), true);

// Get the desired language from the URL parameter
$language = $app_lang;

// Function to get the translation for a given placeholder and language
function translate($placeholder, $language, $translations) {
    return isset($translations['placeholders'][$placeholder][$language]) ? $translations['placeholders'][$placeholder][$language] : $translations['placeholders'][$placeholder]['en'];
}
?>


<div class="container form py-3 bg-transparent">
    <h3 class="mb-3">Profile</h3>
    <!-- User Information -->
    <p class="my-1"><strong>Full Name:</strong> <?php echo $full_name; ?></p>
    <p class="my-1"><strong>Username:</strong> <?php echo $username; ?></p>
    <p class="my-1"><strong>Gender:</strong> <?php echo $gender; ?></p>
    <p class="my-1"><strong>Age:</strong> <?php echo $age; ?></p>
    <p class="my-1"><strong>Weight:</strong> <?php echo $weight; ?> <span>kg</span></p>
    <p class="my-1"><strong>Height:</strong> <?php echo $height; ?> <span>cm</span></p>
    <p class="my-1"><strong>Medical Condition:</strong> <?php echo $medical_condition; ?></p>
    <p class="my-1 d-inline"><strong>App Language:</strong>&nbsp;<?php echo $language_name; ?><img class="mx-2" src="<?php echo $flag_path; ?>" alt="<?php echo $app_lang; ?>" width="20"></p>


    <!-- Buttons to trigger modals -->
    <div class="w-100 text-center my-3 centered">
        <!-- Friend List Modal Trigger -->
        <button type="button" class="btn btn-outline-dark rounded-pill border-3 px-3 py-1 mt-2 position-relative" data-bs-toggle="modal" data-bs-target="#friendListModal">
            <span class="bi bi-people-fill display-6 d-inline"></span>
            <span class="">Friends</span>
            <?php if ($has_pending_requests): ?>
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning p-2">
                    <?php echo $request_result->num_rows; ?>
                </span>
            <?php endif; ?>
        </button>
    </div>
    <!-- Edit and Logout buttons -->
    <p class="mt-2 mb-3 centerH">
        <a class="btn lh-1 rounded-pill p-2 mx-1 my-1 cButton" href="<?php echo url('pages/edit_profile.php'); ?>"><span class="lh-1 mx-1 bi bi-pencil"></span> Edit</a>
        <a class="btn lh-1 rounded-pill p-2 mx-1 my-1 cButton" href="#profile" id="securityButton"><span class="lh-1 mx-1 bi bi-shield-lock"></span> Security</a>
        <a class="btn lh-1 rounded-pill p-2 mx-1 my-1 cButton text-danger bg-danger" href="#profile" onclick="confirmLogout()"><span class="lh-1 mx-1 bi bi-box-arrow-right"></span> Logout</a>
    </p>

</div>

<!-- JavaScript for Security Button and Logout Confirmation -->

<script>
    var appLang = '<?php echo htmlspecialchars($app_lang, ENT_QUOTES, 'UTF-8'); ?>';
    $(document).ready(function() {
        $("#securityButton").click(function() {
            var passwordPrompt;
            fetch('<?php echo url('assets/lang/'); ?>' + appLang + '.json')
                .then(response => response.json())
                .then(data => {
                    passwordPrompt = data["EnterPassword"];
                    var password = prompt(passwordPrompt);
                    if (password !== null && password !== "") {
                        $.post("<?php echo url('api/authenticate_password.php'); ?>", { password: password }, function(response) {
                            if (response === "success") {
                                window.location.href = "<?php echo url('pages/edit_security.php'); ?>";
                            } else {
                                alert(data["IncorrectPassword"]);
                            }
                        });
                    }
                })
                .catch(error => console.error('Error loading translation JSON:', error));
        });
    });

    function confirmLogout() {
        fetch('<?php echo url('assets/lang/'); ?>' + appLang + '.json')
            .then(response => response.json())
            .then(data => {
                if (window.confirm(data["ConfirmLogout"])) {
                    window.location.href = "<?php echo url('logout.php'); ?>";
                }
            })
            .catch(error => console.error('Error loading translation JSON:', error));
    }
</script>


<!-- Modals Section -->
<center>
    <!-- Friend List Modal -->
    <div class="modal fade" id="friendListModal" tabindex="-1" aria-labelledby="friendListModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content rounded-0 p-2">
                <div class="modal-header d-flex justify-content-center">
                    <h5 class="modal-title" id="friendListModalLabel">Your Friends</h5>
                </div>
                <div class="modal-body py-2">
                    <!-- Pending Friend Requests -->
                    <?php if ($has_pending_requests): ?>
                        <h6>Friend Requests</h6>
                        <ul class="list-group mb-3 mt-1">
                            <?php while($request = $request_result->fetch_assoc()): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-2" style="background-color: #FFF3E0;">
                                    <span><?php echo htmlspecialchars($request['full_name']); ?></span>
                                    <div>
                                        <button class="rounded-pill mx-1 btn btn-success btn-sm accept-request" data-requester-id="<?php echo $request['user_id']; ?>"><i class="bi bi-check h3 px-1"></i></button>
                                        <button class="rounded-pill mx-1 btn btn-danger btn-sm decline-request" data-requester-id="<?php echo $request['user_id']; ?>"><i class="bi bi-x h3 px-1"></i></button>
                                    </div>
                                </li>
                            <?php endwhile; ?>
                        </ul>
                    <?php endif; ?>

                    <!-- List of Friends -->
                    <?php if ($friend_result->num_rows > 0): ?>
                        <ul class="list-group">
                            <?php while($friend = $friend_result->fetch_assoc()): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-1">
                                    <div>
                                        <strong><?php echo $friend['full_name']; ?></strong><br>
                                        (@<?php echo $friend['username']; ?>) [<?php echo $friend['role']; ?>] BMI(<?php echo $friend['bmi']; ?>)
                                    </div>
                                    <span class="badge text-yellow bg-green rounded-circle p-2">
                                        <?php echo $friend['health_status'] ?? '∅'; ?>
                                    </span>
                                </li>
                            <?php endwhile; ?>
                        </ul>
                    <?php else: ?>
                        <p>You have no friends yet.</p>
                    <?php endif; ?>
                </div>
                <div class="modal-footer py-2 d-flex flex-column align-items-center">
                    <!-- Add Friend Options -->
                    <div class="mb-2 w-100 d-flex">
                        <!-- QR Code Modal Trigger -->
                        <button type="button" class="btn btn-default mx-2" data-bs-toggle="modal" data-bs-target="#qrModal">
                            <img src="<?php echo url('assets/img/QRcode.png'); ?>" alt="Show QR Code" style="width: 50px;">
                        </button>
                        <div class="input-group py-1">
                            <input type="text" id="usernameToAdd" class="form-control rounded-pill px-2 mx-1" placeholder="<?php echo translate('Username', $language, $translations); ?>" aria-label="Username">
                            <button class="btn rounded-pill px-2 mx-1 cButton" type="button" id="addFriendButton">Add Friend</button>
                        </div>
                    </div>
                </div>
                <hr class="text-body-tertiary">
                <div class="d-flex justify-content-center mt-3 mb-1">
                    <button type="button" class="btn btn-secondary rounded-pill p-1 px-2 mx-2" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

<?php include "components/qr_code.php"; ?>
</center>

<!-- JavaScript for Friend Addition and Friend Requests -->
<script>
    $(document).ready(function() {
        // Handle Add Friend by Username
        $('#addFriendButton').click(function() {
            var usernameToAdd = $('#usernameToAdd').val().trim();
            if (usernameToAdd !== '') {
                $.ajax({
                    url: '<?php echo url('api/add_friend.php'); ?>',
                    type: 'POST',
                    data: { friend_username: usernameToAdd },
                    success: function(response) {
                        alert(response);
                        // Optionally, refresh the friend list
                        // location.reload();
                    },
                    error: function() {
                        showTranslatedAlert('Error sending friend request.');
                    }
                });
            } else {
                showTranslatedAlert('Please enter a username.');
            }
        });

        // Adjust the QR Code Scanner to Add Friend
        function onScanSuccess(decodedText, decodedResult) {
            $.ajax({
                url: '<?php echo url('api/add_friend.php'); ?>',
                type: 'POST',
                data: { account_number: decodedText },
                success: function(response) {
                    alert(response);
                    $('#qrScannerModal').modal('hide');
                    // Optionally, refresh the friend list
                    location.reload();
                },
                error: function() {
                    showTranslatedAlert('Error adding friend.');
                }
            });
        }

        // Initialize QR Code Scanner
        let html5QrCode;
        $('#qrScannerModal').on('shown.bs.modal', function () {
            html5QrCode = new Html5Qrcode("reader");
            html5QrCode.start({ facingMode: "environment" }, {
                fps: 10,
                qrbox: 250
            }, onScanSuccess).catch(err => {
                console.log(err);
            });
        });

        // Stop the scanner when modal is hidden
        $('#qrScannerModal').on('hidden.bs.modal', function () {
            if (html5QrCode) {
                html5QrCode.stop().then(ignore => {
                    console.log("QR Code scanning stopped.");
                }).catch(err => {
                    console.log(err);
                });
                html5QrCode.clear();
            }
        });

        // Accept Friend Request
        $('.accept-request').click(function() {
            var requesterId = $(this).data('requester-id');
            $.ajax({
                url: '<?php echo url('api/manage_friend_request.php'); ?>',
                type: 'POST',
                data: { requester_id: requesterId, action: 'accept' },
                success: function(response) {
                    alert(response);
                    location.reload();
                },
                error: function() {
                    showTranslatedAlert('Error accepting friend request.');
                }
            });
        });

        // Decline Friend Request
        $('.decline-request').click(function() {
            var requesterId = $(this).data('requester-id');
            $.ajax({
                url: '<?php echo url('api/manage_friend_request.php'); ?>',
                type: 'POST',
                data: { requester_id: requesterId, action: 'reject' },
                success: function(response) {
                    alert(response);
                    location.reload();
                },
                error: function() {
                    showTranslatedAlert('Error declining friend request.');
                }
            });
        });
    });
</script>

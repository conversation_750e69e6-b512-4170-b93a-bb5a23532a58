<?php
require 'includes/db.php';
include('components/translate_alert.php');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: login.php');
    exit();
}

// Fetch user details from the database
$username = $_SESSION['username'];
$query = "SELECT user_id, account_number FROM users WHERE username = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("s", $username);
$stmt->execute();
$user_result = $stmt->get_result();

if ($user_result->num_rows > 0) {
    $user = $user_result->fetch_assoc();
    $user_id = $user['user_id'];
    $account_number = $user['account_number'];
} else {
    echo "User not found!";
    exit();
}

// Retrieve user information
$query = "SELECT * FROM users WHERE username = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $full_name = $row['full_name'];
    $username = $row['username'];
    $gender = $row['gender'];
    $birth_date = $row['birth_date'];
    $weight = $row['weight'];
    $height = $row['height'];
    $medical_condition = $row['medical_condition'];
    $app_lang = $row['app_lang'];

    // Calculate age from birthdate
    $birthdate = new DateTime($birth_date);
    $currentDate = new DateTime();
    $age = $currentDate->diff($birthdate)->y;

    // Function to get language name and flag image path
    function getLanguageInfo($app_lang) {
        switch ($app_lang) {
            case 'en':
                return ['English', url('assets/img/en.png')];
            case 'ar':
                return ['Arabic', url('assets/img/ar.png')];
            case 'ckb':
                return ['Kurdish', url('assets/img/ckb.png')];
            case 'kmr':
                return ['Kurdish', url('assets/img/ckb.png')];
            case 'fa':
                return ['Persian', url('assets/img/fa.png')];
            case 'tr':
                return ['Turkish', url('assets/img/tr.png')];
            default:
                return ['Unknown', ''];
        }
    }

    // Get language name and flag image path
    list($language_name, $flag_path) = getLanguageInfo($app_lang);
} else {
    echo "User not found!";
    exit();
}

// Fetch user's friends and their health_status for the current date only
$friend_query = "
    SELECT u.user_id, u.full_name, u.username, u.role, u.bmi, un.health_status
    FROM friends f
    JOIN users u ON f.friend_id = u.user_id
    LEFT JOIN user_nutrition un ON un.user_id = u.user_id AND un.input_date = CURDATE()
    WHERE f.user_id = ? AND f.status = 'accepted'
";
$stmt = $con->prepare($friend_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$friend_result = $stmt->get_result();


// Fetch incoming friend requests
$request_query = "
    SELECT u.user_id, u.username, u.full_name
    FROM friends f
    JOIN users u ON f.user_id = u.user_id
    WHERE f.friend_id = ? AND f.status = 'pending'
";
$stmt = $con->prepare($request_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$request_result = $stmt->get_result();

// Check for pending requests
$has_pending_requests = $request_result->num_rows > 0;
?>


<?php
// Load the translations from the JSON file
$translations = json_decode(file_get_contents('assets/lang/placeholders.json'), true);

// Get the desired language from the URL parameter
$language = $app_lang;

// Function to get the translation for a given placeholder and language
if (!function_exists('translate')) {
    function translate($placeholder, $language, $translations) {
        return isset($translations['placeholders'][$placeholder][$language]) ? $translations['placeholders'][$placeholder][$language] : $translations['placeholders'][$placeholder]['en'];
    }
}
?>


<div class="container-fluid profile-container px-4 pt-xl-1" style="padding-top: 500px !important;">
    <!-- Profile Information -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="px-3 py-2">
                <h5 class="mb-4 fw-semibold text-dark">
                    <i class="bi bi-person-circle me-3 text-primary"></i>Profile Information
                </h5>
                <div class="row gx-3 gy-3">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center p-3 bg-light rounded-3">
                            <i class="bi bi-person-badge me-4 text-primary fs-4"></i>
                            <div>
                                <small class="text-muted d-block mb-1">Full Name</small>
                                <span class="fw-medium"><?php echo htmlspecialchars($full_name); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center p-3 bg-light rounded-3">
                            <i class="bi bi-at me-4 text-primary fs-4"></i>
                            <div>
                                <small class="text-muted d-block mb-1">Username</small>
                                <span class="fw-medium">@<?php echo htmlspecialchars($username); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-lg-3">
                        <div class="d-flex align-items-center p-3 bg-light rounded-3">
                            <i class="bi bi-gender-ambiguous me-4 text-primary fs-4"></i>
                            <div>
                                <small class="text-muted d-block mb-1">Gender</small>
                                <span class="fw-medium"><?php echo ucfirst(htmlspecialchars($gender)); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-lg-3">
                        <div class="d-flex align-items-center p-3 bg-light rounded-3">
                            <i class="bi bi-calendar-event me-4 text-primary fs-4"></i>
                            <div>
                                <small class="text-muted d-block mb-1">Age</small>
                                <span class="fw-medium"><?php echo $age; ?> years</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-lg-3">
                        <div class="d-flex align-items-center p-3 bg-light rounded-3">
                            <i class="bi bi-speedometer2 me-4 text-primary fs-4"></i>
                            <div>
                                <small class="text-muted d-block mb-1">Weight</small>
                                <span class="fw-medium"><?php echo $weight; ?> kg</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-lg-3">
                        <div class="d-flex align-items-center p-3 bg-light rounded-3">
                            <i class="bi bi-rulers me-4 text-primary fs-4"></i>
                            <div>
                                <small class="text-muted d-block mb-1">Height</small>
                                <span class="fw-medium"><?php echo $height; ?> cm</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-6">
                        <div class="d-flex align-items-center p-3 bg-light rounded-3">
                            <i class="bi bi-heart-pulse me-4 text-primary fs-4"></i>
                            <div>
                                <small class="text-muted d-block mb-1">Medical Condition</small>
                                <span class="fw-medium"><?php echo htmlspecialchars($medical_condition); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-6">
                        <div class="d-flex align-items-center p-3 bg-light rounded-3">
                            <i class="bi bi-translate me-4 text-primary fs-4"></i>
                            <div>
                                <small class="text-muted d-block mb-1">App Language</small>
                                <div class="d-flex align-items-center mt-1">
                                    <img src="<?php echo $flag_path; ?>" alt="<?php echo $language_name; ?>"
                                        class="me-2" style="width: 20px; height: auto;">
                                    <span class="fw-medium"><?php echo $language_name; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Cards -->
    <div class="row g-4 mt-4 mb-5">
        <!-- Account Management Card -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100 mx-2">
                <div class="card-header bg-white border-bottom-0 py-4 px-4">
                    <h6 class="mb-0 fw-semibold text-dark">
                        <i class="bi bi-gear me-3 text-success"></i>Account Management
                    </h6>
                </div>
                <div class="card-body p-4">
                    <div class="d-grid gap-3">
                        <a href="<?php echo url('pages/edit_profile.php'); ?>"
                            class="btn btn-outline-primary d-flex align-items-center justify-content-center py-3 rounded-3">
                            <i class="bi bi-pencil-square me-2"></i>
                            <span>Edit Profile</span>
                        </a>
                        <button id="securityButton"
                            class="btn btn-outline-warning d-flex align-items-center justify-content-center py-3 rounded-3">
                            <i class="bi bi-shield-lock me-2"></i>
                            <span>Security Settings</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Actions Card -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100 mx-2">
                <div class="card-header bg-white border-bottom-0 py-4 px-4">
                    <h6 class="mb-0 fw-semibold text-dark">
                        <i class="bi bi-power me-3 text-danger"></i>System Actions
                    </h6>
                </div>
                <div class="card-body p-4">
                    <div class="d-grid gap-3">
                        <button onclick="confirmLogout()"
                            class="btn btn-outline-danger d-flex align-items-center justify-content-center py-3 rounded-3">
                            <i class="bi bi-box-arrow-right me-2"></i>
                            <span>Sign Out</span>
                        </button>
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                You'll be redirected to login
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- JavaScript for Security Button and Logout Confirmation -->

<script>
var appLang = '<?php echo htmlspecialchars($app_lang, ENT_QUOTES, 'UTF-8'); ?>';
$(document).ready(function() {
    $("#securityButton").click(function() {
        var passwordPrompt;
        fetch('<?php echo url('assets/lang/'); ?>' + appLang + '.json')
            .then(response => response.json())
            .then(data => {
                passwordPrompt = data["EnterPassword"];
                var password = prompt(passwordPrompt);
                if (password !== null && password !== "") {
                    $.post("<?php echo url('api/authenticate_password.php'); ?>", {
                        password: password
                    }, function(response) {
                        if (response === "success") {
                            window.location.href =
                                "<?php echo url('pages/edit_security.php'); ?>";
                        } else {
                            alert(data["IncorrectPassword"]);
                        }
                    });
                }
            })
            .catch(error => console.error('Error loading translation JSON:', error));
    });
});

function confirmLogout() {
    fetch('<?php echo url('assets/lang/'); ?>' + appLang + '.json')
        .then(response => response.json())
        .then(data => {
            if (window.confirm(data["ConfirmLogout"])) {
                window.location.href = "<?php echo url('logout.php'); ?>";
            }
        })
        .catch(error => console.error('Error loading translation JSON:', error));
}
</script>
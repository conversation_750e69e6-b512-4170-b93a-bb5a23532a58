<?php
require 'includes/db.php';
include('components/translate_alert.php');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: login.php');
    exit();
}

// Fetch user details from the database
$username = $_SESSION['username'];
$query = "SELECT user_id, account_number FROM users WHERE username = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("s", $username);
$stmt->execute();
$user_result = $stmt->get_result();

if ($user_result->num_rows > 0) {
    $user = $user_result->fetch_assoc();
    $user_id = $user['user_id'];
    $account_number = $user['account_number'];
} else {
    echo "User not found!";
    exit();
}

// Retrieve user information
$query = "SELECT * FROM users WHERE username = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $full_name = $row['full_name'];
    $username = $row['username'];
    $gender = $row['gender'];
    $birth_date = $row['birth_date'];
    $weight = $row['weight'];
    $height = $row['height'];
    $medical_condition = $row['medical_condition'];
    $app_lang = $row['app_lang'];

    // Calculate age from birthdate
    $birthdate = new DateTime($birth_date);
    $currentDate = new DateTime();
    $age = $currentDate->diff($birthdate)->y;

    // Function to get language name and flag image path
    function getLanguageInfo($app_lang) {
        switch ($app_lang) {
            case 'en':
                return ['English', url('assets/img/en.png')];
            case 'ar':
                return ['Arabic', url('assets/img/ar.png')];
            case 'ckb':
                return ['Kurdish', url('assets/img/ckb.png')];
            case 'kmr':
                return ['Kurdish', url('assets/img/ckb.png')];
            case 'fa':
                return ['Persian', url('assets/img/fa.png')];
            case 'tr':
                return ['Turkish', url('assets/img/tr.png')];
            default:
                return ['Unknown', ''];
        }
    }

    // Get language name and flag image path
    list($language_name, $flag_path) = getLanguageInfo($app_lang);
} else {
    echo "User not found!";
    exit();
}

// Fetch user's friends and their health_status for the current date only
$friend_query = "
    SELECT u.user_id, u.full_name, u.username, u.role, u.bmi, un.health_status
    FROM friends f
    JOIN users u ON f.friend_id = u.user_id
    LEFT JOIN user_nutrition un ON un.user_id = u.user_id AND un.input_date = CURDATE()
    WHERE f.user_id = ? AND f.status = 'accepted'
";
$stmt = $con->prepare($friend_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$friend_result = $stmt->get_result();


// Fetch incoming friend requests
$request_query = "
    SELECT u.user_id, u.username, u.full_name
    FROM friends f
    JOIN users u ON f.user_id = u.user_id
    WHERE f.friend_id = ? AND f.status = 'pending'
";
$stmt = $con->prepare($request_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$request_result = $stmt->get_result();

// Check for pending requests
$has_pending_requests = $request_result->num_rows > 0;
?>


<?php
// Load the translations from the JSON file
$translations = json_decode(file_get_contents('assets/lang/placeholders.json'), true);

// Get the desired language from the URL parameter
$language = $app_lang;

// Function to get the translation for a given placeholder and language
if (!function_exists('translate')) {
    function translate($placeholder, $language, $translations) {
        return isset($translations['placeholders'][$placeholder][$language]) ? $translations['placeholders'][$placeholder][$language] : $translations['placeholders'][$placeholder]['en'];
    }
}
?>


<div class="container-fluid px-4 py-4">
    <!-- Modern Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center mb-3">
                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-person-fill text-white fs-3"></i>
                </div>
                <div>
                    <h2 class="mb-0 fw-bold text-dark">Settings</h2>
                    <p class="text-muted mb-0">Manage your account and preferences</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Information Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom-0 py-3">
                    <h5 class="mb-0 fw-semibold text-dark">
                        <i class="bi bi-person-circle me-2 text-primary"></i>Profile Information
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                <i class="bi bi-person-badge me-3 text-primary fs-5"></i>
                                <div>
                                    <small class="text-muted d-block">Full Name</small>
                                    <span class="fw-medium"><?php echo htmlspecialchars($full_name); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                <i class="bi bi-at me-3 text-primary fs-5"></i>
                                <div>
                                    <small class="text-muted d-block">Username</small>
                                    <span class="fw-medium">@<?php echo htmlspecialchars($username); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                <i class="bi bi-gender-ambiguous me-3 text-primary fs-5"></i>
                                <div>
                                    <small class="text-muted d-block">Gender</small>
                                    <span class="fw-medium"><?php echo ucfirst(htmlspecialchars($gender)); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                <i class="bi bi-calendar-event me-3 text-primary fs-5"></i>
                                <div>
                                    <small class="text-muted d-block">Age</small>
                                    <span class="fw-medium"><?php echo $age; ?> years</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                <i class="bi bi-speedometer2 me-3 text-primary fs-5"></i>
                                <div>
                                    <small class="text-muted d-block">Weight</small>
                                    <span class="fw-medium"><?php echo $weight; ?> kg</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                <i class="bi bi-rulers me-3 text-primary fs-5"></i>
                                <div>
                                    <small class="text-muted d-block">Height</small>
                                    <span class="fw-medium"><?php echo $height; ?> cm</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                <i class="bi bi-heart-pulse me-3 text-primary fs-5"></i>
                                <div>
                                    <small class="text-muted d-block">Medical Condition</small>
                                    <span class="fw-medium"><?php echo htmlspecialchars($medical_condition); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                <i class="bi bi-translate me-3 text-primary fs-5"></i>
                                <div>
                                    <small class="text-muted d-block">App Language</small>
                                    <div class="d-flex align-items-center">
                                        <img src="<?php echo $flag_path; ?>" alt="<?php echo $language_name; ?>" class="me-2" style="width: 20px; height: auto;">
                                        <span class="fw-medium"><?php echo $language_name; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit and Logout buttons -->
    <p class="mt-2 mb-3 centerH">
        <a class="btn lh-1 rounded-pill p-2 mx-1 my-1 cButton" href="<?php echo url('pages/edit_profile.php'); ?>"><span class="lh-1 mx-1 bi bi-pencil"></span> Edit</a>
        <a class="btn lh-1 rounded-pill p-2 mx-1 my-1 cButton" href="#profile" id="securityButton"><span class="lh-1 mx-1 bi bi-shield-lock"></span> Security</a>
        <a class="btn lh-1 rounded-pill p-2 mx-1 my-1 cButton text-danger bg-danger" href="#profile" onclick="confirmLogout()"><span class="lh-1 mx-1 bi bi-box-arrow-right"></span> Logout</a>
    </p>

</div>

<!-- JavaScript for Security Button and Logout Confirmation -->

<script>
    var appLang = '<?php echo htmlspecialchars($app_lang, ENT_QUOTES, 'UTF-8'); ?>';
    $(document).ready(function() {
        $("#securityButton").click(function() {
            var passwordPrompt;
            fetch('<?php echo url('assets/lang/'); ?>' + appLang + '.json')
                .then(response => response.json())
                .then(data => {
                    passwordPrompt = data["EnterPassword"];
                    var password = prompt(passwordPrompt);
                    if (password !== null && password !== "") {
                        $.post("<?php echo url('api/authenticate_password.php'); ?>", { password: password }, function(response) {
                            if (response === "success") {
                                window.location.href = "<?php echo url('pages/edit_security.php'); ?>";
                            } else {
                                alert(data["IncorrectPassword"]);
                            }
                        });
                    }
                })
                .catch(error => console.error('Error loading translation JSON:', error));
        });
    });

    function confirmLogout() {
        fetch('<?php echo url('assets/lang/'); ?>' + appLang + '.json')
            .then(response => response.json())
            .then(data => {
                if (window.confirm(data["ConfirmLogout"])) {
                    window.location.href = "<?php echo url('logout.php'); ?>";
                }
            })
            .catch(error => console.error('Error loading translation JSON:', error));
    }
</script>



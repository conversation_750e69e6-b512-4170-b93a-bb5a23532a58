<?php
// Friends Modal Component
// This component requires database connection and user session to be available

if (!isset($_SESSION['user_id'])) {
    return; // Don't show modal if user is not logged in
}

$user_id = $_SESSION['user_id'];

// Fetch pending friend requests using the friends table
$request_query = "SELECT f.user_id as requester_id, u.full_name, u.username
                  FROM friends f
                  JOIN users u ON f.user_id = u.user_id
                  WHERE f.friend_id = ? AND f.status = 'pending'";
$request_stmt = $con->prepare($request_query);
$request_stmt->bind_param("i", $user_id);
$request_stmt->execute();
$request_result = $request_stmt->get_result();
$has_pending_requests = $request_result->num_rows > 0;

// Fetch friends list with health status using the friends table
$friend_query = "SELECT u.full_name, u.username,
                        'Friend' as role,
                        ROUND((u.weight / POWER(u.height/100, 2)), 1) as bmi,
                        CASE
                            WHEN ROUND((u.weight / POWER(u.height/100, 2)), 1) < 18.5 THEN '⚠️'
                            WHEN ROUND((u.weight / POWER(u.height/100, 2)), 1) BETWEEN 18.5 AND 24.9 THEN '✅'
                            WHEN ROUND((u.weight / POWER(u.height/100, 2)), 1) BETWEEN 25 AND 29.9 THEN '⚠️'
                            ELSE '❌'
                        END as health_status
                 FROM friends f
                 JOIN users u ON f.friend_id = u.user_id
                 WHERE f.user_id = ? AND f.status = 'accepted'";
$friend_stmt = $con->prepare($friend_query);
$friend_stmt->bind_param("i", $user_id);
$friend_stmt->execute();
$friend_result = $friend_stmt->get_result();

// Get user's account number for QR code
$account_query = "SELECT account_number FROM users WHERE user_id = ?";
$account_stmt = $con->prepare($account_query);
$account_stmt->bind_param("i", $user_id);
$account_stmt->execute();
$account_result = $account_stmt->get_result();
$account_number = $account_result->fetch_assoc()['account_number'] ?? '';

// Load translations for placeholders
$translations = [];
if (isset($_SESSION['username'])) {
    $username = $_SESSION['username'];
    $lang_query = "SELECT app_lang FROM users WHERE username = ?";
    $lang_stmt = $con->prepare($lang_query);
    $lang_stmt->bind_param("s", $username);
    $lang_stmt->execute();
    $lang_result = $lang_stmt->get_result();

    if ($lang_result->num_rows > 0) {
        $lang_row = $lang_result->fetch_assoc();
        $language = $lang_row['app_lang'];

        $translations_file = __DIR__ . '/../assets/lang/placeholders.json';
        if (file_exists($translations_file)) {
            $translations = json_decode(file_get_contents($translations_file), true);
        }
    }
}

function translate($placeholder, $language, $translations) {
    return isset($translations['placeholders'][$placeholder][$language])
        ? $translations['placeholders'][$placeholder][$language]
        : $translations['placeholders'][$placeholder]['en'] ?? $placeholder;
}
?>

<!-- Friend List Modal -->
<div class="modal fade" id="friendListModal" tabindex="-1" aria-labelledby="friendListModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content rounded-4 border-0 shadow">
            <div class="modal-header bg-primary text-white rounded-top-4">
                <h5 class="modal-title fw-bold" id="friendListModalLabel">
                    <i class="bi bi-people-fill me-2"></i>Your Friends
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <!-- Pending Friend Requests -->
                <?php if ($has_pending_requests): ?>
                    <div class="mb-4">
                        <h6 class="fw-semibold text-warning mb-3">
                            <i class="bi bi-clock-history me-2"></i>Pending Requests
                        </h6>
                        <div class="list-group">
                            <?php while($request = $request_result->fetch_assoc()): ?>
                                <div class="list-group-item border-0 bg-warning bg-opacity-10 rounded-3 mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong class="text-dark"><?php echo htmlspecialchars($request['full_name']); ?></strong>
                                            <small class="text-muted d-block">@<?php echo htmlspecialchars($request['username']); ?></small>
                                        </div>
                                        <div>
                                            <button class="btn btn-success btn-sm me-1" onclick="manageFriendRequest(<?php echo $request['requester_id']; ?>, 'accept')">
                                                <i class="bi bi-check-lg"></i>
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="manageFriendRequest(<?php echo $request['requester_id']; ?>, 'reject')">
                                                <i class="bi bi-x-lg"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Friends List -->
                <div class="mb-4">
                    <h6 class="fw-semibold text-primary mb-3">
                        <i class="bi bi-people me-2"></i>Friends List
                    </h6>
                    <?php if ($friend_result->num_rows > 0): ?>
                        <div class="list-group">
                            <?php while($friend = $friend_result->fetch_assoc()): ?>
                                <div class="list-group-item border-0 bg-light rounded-3 mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong class="text-dark"><?php echo htmlspecialchars($friend['full_name']); ?></strong>
                                            <small class="text-muted d-block">@<?php echo htmlspecialchars($friend['username']); ?> • BMI: <?php echo $friend['bmi']; ?></small>
                                        </div>
                                        <span class="fs-4"><?php echo $friend['health_status']; ?></span>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">You have no friends yet.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Add Friend Section -->
                <div class="border-top pt-4">
                    <h6 class="fw-semibold text-success mb-3">
                        <i class="bi bi-person-plus me-2"></i>Add New Friend
                    </h6>
                    <div class="row g-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" id="usernameToAdd" class="form-control border-start-0"
                                       placeholder="<?php echo translate('Username', $language ?? 'en', $translations); ?>"
                                       aria-label="Username">
                                <button class="btn btn-success" type="button" id="addFriendButton">
                                    <i class="bi bi-plus-lg me-1"></i>Add
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <button type="button" class="btn btn-outline-primary w-100" data-bs-toggle="modal" data-bs-target="#qrModal">
                                <i class="bi bi-qr-code me-2"></i>QR Code
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- QR Code Modal -->
<div class="modal fade" id="qrModal" tabindex="-1" aria-labelledby="qrModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content rounded-4 border-0 shadow">
            <div class="modal-header bg-info text-white rounded-top-4">
                <h5 class="modal-title fw-bold" id="qrModalLabel">
                    <i class="bi bi-qr-code me-2"></i>Your QR Code
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-4">
                <div class="bg-white p-3 rounded-3 d-inline-block shadow-sm">
                    <div id="qrcode" class="mb-3"></div>
                </div>
                <p class="text-muted mt-3">Share this QR code with friends to connect</p>
                <small class="text-muted">Account: <?php echo htmlspecialchars($account_number); ?></small>
            </div>
        </div>
    </div>
</div>

<!-- QR Scanner Modal -->
<div class="modal fade" id="qrScannerModal" tabindex="-1" aria-labelledby="qrScannerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content rounded-4 border-0 shadow">
            <div class="modal-header bg-success text-white rounded-top-4">
                <h5 class="modal-title fw-bold" id="qrScannerModalLabel">
                    <i class="bi bi-camera me-2"></i>Scan QR Code
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div id="reader" class="rounded-3 overflow-hidden"></div>
                <p class="text-muted text-center mt-3">Point your camera at a friend's QR code</p>
            </div>
        </div>
    </div>
</div>

<?php
// Friends Modal Component - Original Version
// This component requires database connection and user session to be available

if (!isset($_SESSION['user_id'])) {
    return; // Don't show modal if user is not logged in
}

$user_id = $_SESSION['user_id'];

// Fetch pending friend requests using the friends table
$request_query = "SELECT f.user_id as requester_id, u.full_name, u.username
                  FROM friends f
                  JOIN users u ON f.user_id = u.user_id
                  WHERE f.friend_id = ? AND f.status = 'pending'";
$request_stmt = $con->prepare($request_query);
$request_stmt->bind_param("i", $user_id);
$request_stmt->execute();
$request_result = $request_stmt->get_result();
$has_pending_requests = $request_result->num_rows > 0;

// Fetch friends list with health status using the friends table
$friend_query = "SELECT u.user_id, u.full_name, u.username, u.role, u.bmi, un.health_status
                 FROM friends f
                 JOIN users u ON f.friend_id = u.user_id
                 LEFT JOIN user_nutrition un ON un.user_id = u.user_id AND un.input_date = CURDATE()
                 WHERE f.user_id = ? AND f.status = 'accepted'";
$friend_stmt = $con->prepare($friend_query);
$friend_stmt->bind_param("i", $user_id);
$friend_stmt->execute();
$friend_result = $friend_stmt->get_result();

// Get user's account number for QR code
$account_query = "SELECT account_number FROM users WHERE user_id = ?";
$account_stmt = $con->prepare($account_query);
$account_stmt->bind_param("i", $user_id);
$account_stmt->execute();
$account_result = $account_stmt->get_result();
$account_number = $account_result->fetch_assoc()['account_number'] ?? '';

// Load translations for placeholders
$translations = [];
if (isset($_SESSION['username'])) {
    $username = $_SESSION['username'];
    $lang_query = "SELECT app_lang FROM users WHERE username = ?";
    $lang_stmt = $con->prepare($lang_query);
    $lang_stmt->bind_param("s", $username);
    $lang_stmt->execute();
    $lang_result = $lang_stmt->get_result();

    if ($lang_result->num_rows > 0) {
        $lang_row = $lang_result->fetch_assoc();
        $language = $lang_row['app_lang'];

        $translations_file = __DIR__ . '/../assets/lang/placeholders.json';
        if (file_exists($translations_file)) {
            $translations = json_decode(file_get_contents($translations_file), true);
        }
    }
}

if (!function_exists('translate')) {
    function translate($placeholder, $language, $translations) {
        return isset($translations['placeholders'][$placeholder][$language])
            ? $translations['placeholders'][$placeholder][$language]
            : $translations['placeholders'][$placeholder]['en'] ?? $placeholder;
    }
}
?>

<center>
    <!-- Friend List Modal -->
    <div class="modal fade" id="friendListModal" tabindex="-1" aria-labelledby="friendListModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content rounded-0 p-2">
                <div class="modal-header d-flex justify-content-center">
                    <h5 class="modal-title" id="friendListModalLabel">Your Friends</h5>
                </div>
                <div class="modal-body py-2">
                    <!-- Pending Friend Requests -->
                    <?php if ($has_pending_requests): ?>
                        <h6>Friend Requests</h6>
                        <ul class="list-group mb-3 mt-1">
                            <?php while($request = $request_result->fetch_assoc()): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-2" style="background-color: #FFF3E0;">
                                    <span><?php echo htmlspecialchars($request['full_name']); ?></span>
                                    <div>
                                        <button class="rounded-pill mx-1 btn btn-success btn-sm accept-request" data-requester-id="<?php echo $request['requester_id']; ?>"><i class="bi bi-check h3 px-1"></i></button>
                                        <button class="rounded-pill mx-1 btn btn-danger btn-sm decline-request" data-requester-id="<?php echo $request['requester_id']; ?>"><i class="bi bi-x h3 px-1"></i></button>
                                    </div>
                                </li>
                            <?php endwhile; ?>
                        </ul>
                    <?php endif; ?>

                    <!-- List of Friends -->
                    <?php if ($friend_result->num_rows > 0): ?>
                        <ul class="list-group">
                            <?php while($friend = $friend_result->fetch_assoc()): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-1">
                                    <div>
                                        <strong><?php echo $friend['full_name']; ?></strong><br>
                                        (@<?php echo $friend['username']; ?>) [Friend] BMI(<?php echo $friend['bmi']; ?>)
                                    </div>
                                    <span class="badge text-yellow bg-green rounded-circle p-2">
                                        <?php echo $friend['health_status'] ?? '∅'; ?>
                                    </span>
                                </li>
                            <?php endwhile; ?>
                        </ul>
                    <?php else: ?>
                        <p>You have no friends yet.</p>
                    <?php endif; ?>
                </div>
                <div class="modal-footer py-2 d-flex flex-column align-items-center">
                    <!-- Add Friend Options -->
                    <div class="mb-2 w-100 d-flex">
                        <!-- QR Code Modal Trigger -->
                        <button type="button" class="btn btn-default mx-2" data-bs-toggle="modal" data-bs-target="#qrModal">
                            <img src="<?php echo url('assets/img/QRcode.png'); ?>" alt="Show QR Code" style="width: 50px;">
                        </button>
                        <div class="input-group py-1">
                            <input type="text" id="usernameToAdd" class="form-control rounded-pill px-2 mx-1" placeholder="<?php echo translate('Username', $language ?? 'en', $translations); ?>" aria-label="Username">
                            <button class="btn rounded-pill px-2 mx-1 cButton" type="button" id="addFriendButton">Add Friend</button>
                        </div>
                    </div>
                </div>
                <hr class="text-body-tertiary">
                <div class="d-flex justify-content-center mt-3 mb-1">
                    <button type="button" class="btn btn-secondary rounded-pill p-1 px-2 mx-2" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <?php include "components/qr_code.php"; ?>
</center>

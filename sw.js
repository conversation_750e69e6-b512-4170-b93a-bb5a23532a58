// sw.js (Service Worker)
// Version: 1.6

// Cache name - update this when you make changes to the service worker
const CACHE_NAME = "c-health-cache-v1";

self.addEventListener("install", function (event) {
  // Log the service worker scope
  console.log("Service Worker scope: ", self.registration.scope);

  // Get the base path from the service worker scope
  const basePath = self.registration.scope.replace(/\/$/, "");

  // Wait until the service worker is installed
  event.waitUntil(
    caches.open(CACHE_NAME).then(function (cache) {
      return cache.addAll([
        basePath + "/offline.html",
        basePath + "/assets/css/style-dynamic.php",
        basePath + "/assets/img/logo.png",
        basePath + "/assets/img/fav.ico",
        basePath + "/assets/img/home.png",
        basePath + "/assets/img/statistics.png",
        basePath + "/assets/img/input.png",
        basePath + "/assets/img/profile.png",
        basePath + "/assets/img/guide.png",
        "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css",
        "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js",
        "https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js",
        "https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js",
      ]);
    })
  );
});

// Activate event - clean up old caches
self.addEventListener("activate", function (event) {
  event.waitUntil(
    caches.keys().then(function (cacheNames) {
      return Promise.all(
        cacheNames.map(function (cacheName) {
          if (cacheName !== CACHE_NAME) {
            console.log("Deleting old cache:", cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );

  // Immediately claim clients so the new service worker takes effect right away
  return self.clients.claim();
});

self.addEventListener("fetch", function (event) {
  const url = new URL(event.request.url);

  // Extract the base path from the service worker scope
  const basePath = new URL(self.registration.scope).pathname.replace(/\/$/, "");

  // List of URLs to always fetch from the network (with dynamic base path)
  const authUrls = [
    basePath + "/includes/remember_me.php",
    basePath + "/dashboard.php",
    basePath + "/index.php",
  ];

  // Check if the request is for a CSS file
  const isCssRequest =
    url.pathname.endsWith(".css") ||
    (url.pathname.endsWith(".php") && url.pathname.includes("/css/"));

  if (
    authUrls.includes(url.pathname) ||
    event.request.destination === "document"
  ) {
    // For authentication URLs and HTML documents, fetch from the network
    event.respondWith(
      fetch(event.request)
        .then(function (response) {
          // If the response is a redirect, follow it without caching
          if (response.redirected) {
            return fetch(response.url);
          }
          return response;
        })
        .catch(function (error) {
          // Handle network errors
          console.error("Fetch failed; returning offline page instead.", error);
          return caches
            .match(basePath + "/offline.html")
            .then(function (response) {
              return (
                response ||
                new Response(
                  "You are offline and the offline page is not available."
                )
              );
            });
        })
    );
  } else if (isCssRequest) {
    // For CSS files, use network-first strategy to ensure latest styles
    event.respondWith(
      fetch(event.request)
        .then(function (response) {
          // Clone the response to store in cache
          const responseToCache = response.clone();

          // Store the fetched response in the cache
          caches.open(CACHE_NAME).then(function (cache) {
            cache.put(event.request, responseToCache);
          });

          return response;
        })
        .catch(function (error) {
          // If network fetch fails, try to get from cache
          return caches.match(event.request);
        })
    );
  } else {
    // Cache-first strategy for other requests (images, scripts, etc.)
    event.respondWith(
      caches.match(event.request).then(function (response) {
        return (
          response ||
          fetch(event.request)
            .then(function (networkResponse) {
              // Store the fetched response in the cache
              caches.open(CACHE_NAME).then(function (cache) {
                cache.put(event.request, networkResponse.clone());
              });
              return networkResponse;
            })
            .catch(function (error) {
              console.error("Fetch failed for resource:", error);
              // For image requests, use the logo as a placeholder
              if (event.request.destination === "image") {
                return caches.match(basePath + "/assets/img/logo.png");
              }
              return new Response("Resource not available offline");
            })
        );
      })
    );
  }
});

<?php
include("../includes/auth_session.php");
require_once '../includes/config.php';
require('../includes/db.php');

$username = $_SESSION['username'];

// Check if the form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Retrieve form data
    $full_name = $_POST['full_name'];
    $gender = $_POST['gender'];
    $birth_date = $_POST['birth_date'];
    $weight = $_POST['weight'];
    $height = $_POST['height'];

    // Initialize an array to store medical conditions
    $medical_conditions = [];

    // Check if medical conditions are selected
    if(isset($_POST['medical_condition']) && is_array($_POST['medical_condition'])) {
        // Loop through each selected medical condition
        foreach ($_POST['medical_condition'] as $condition) {
            // Add the selected medical condition to the array
            $medical_conditions[] = $condition;
        }
    }

    // Determine medical conditions based on BMI and add them
    $bmi = calculateBMI($weight, $height);
    if ($bmi < 18.5) {
        $medical_conditions[] = 'Underweight';
    } elseif ($bmi <= 29.9 && $bmi >= 24.9) {
        $medical_conditions[] = 'Overweight';
    } elseif ($bmi >= 30) {
        $medical_conditions[] = 'Obesity';
    } else {
        $medical_conditions[] = 'Healthy';
    }

    // Remove "Healthy" condition if Cardiovascular Disease, Diabetes, or Hypertension is selected
    if (in_array('Cardiovascular Disease', $medical_conditions) || in_array('Diabetes', $medical_conditions) || in_array('Hypertension', $medical_conditions)) {
        $key = array_search('Healthy', $medical_conditions);
        if ($key !== false) {
            unset($medical_conditions[$key]);
        }
    }

    // Implode the array to store medical conditions as a comma-separated string
    $medical_condition = implode(', ', $medical_conditions);

    // Retrieve selected language from the form
    $app_lang = $_POST['app_lang'];

    // Update user information in the database
    $query = "UPDATE users SET full_name=?, gender=?, birth_date=?, weight=?, height=?, medical_condition=?, bmi=?, app_lang=? WHERE username=?";
    $stmt = $con->prepare($query);
    $stmt->bind_param("sssssssss", $full_name, $gender, $birth_date, $weight, $height, $medical_condition, $bmi, $app_lang, $username);

    if ($stmt->execute()) {
        // Redirect to the profile page after successful update
        header("Location: " . url('dashboard.php'));
        exit();
    } else {
        // Handle the case when the update fails
        echo "Error updating profile. Please try again.";
    }

    $stmt->close();
}

// Fetch current user data to pre-fill the form
$query = "SELECT * FROM users WHERE username = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $full_name = $row['full_name'];
    $gender = $row['gender'];
    $birth_date = $row['birth_date'];
    $weight = $row['weight'];
    $height = $row['height'];
} else {
    echo "User not found!";
    exit();
}

$stmt->close();

// Function to calculate BMI
function calculateBMI($weight, $height) {
    $heightInMeters = $height / 100;
    return $weight / ($heightInMeters * $heightInMeters);
}




$username = $_SESSION['username'];

// Set default direction (left to right)
$dir = 'ltr'; // Default direction (left to right)

// Check if the user is signed in and get their language preference from the database
if (isset($_SESSION['username'])) {
    $query = "SELECT app_lang FROM users WHERE username = ?";
    $stmt = $con->prepare($query);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $app_lang = $row['app_lang'];

        // Set direction based on language
        if ($app_lang === 'ckb' || $app_lang === 'ar' || $app_lang === 'fa') {
            $dir = 'rtl';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="<?php echo isset($app_lang) ? $app_lang : 'en'; ?>" dir="<?php echo $dir; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C-Health</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://getbootstrap.com/docs/5.3/assets/css/docs.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo url('assets/css/style-dynamic.php'); ?>">
    <link rel="icon" type="image/x-icon" href="<?php echo url('assets/img/fav.ico'); ?>">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>

    <!-- Edit Profile Form -->
    <form class="form p-3" action="" method="post">
    <center><img src="<?php echo url('assets/img/logo.png'); ?>" alt="logo" width="100%"></center>
        <h1 class="login-title"><ion-icon name="create-outline"></ion-icon> Edit Profile</h1>

        <!-- Full Name Field -->
        <div class="mb-3">
            <label for="full_name" class="form-label">Full Name</label>
            <input type="text" class="form-control p-2 rounded-pill" id="full_name" name="full_name" value="<?php echo $full_name; ?>" required>
        </div>

        <!-- Gender Field -->
        <div class="my-3">
            <label for="gender" class="form-label">Gender:</label>
            <select class="form-select p-2 rounded-pill" id="gender" name="gender" required>
                <option value="male" <?php echo ($gender == 'male') ? 'selected' : ''; ?>>Male</option>
                <option value="female" <?php echo ($gender == 'female') ? 'selected' : ''; ?>>Female</option>
            </select>
        </div>

        <!-- Birthdate Field -->
        <div class="mb-3">
            <label for="birth_date" class="form-label">Birthdate:</label>
            <input type="date" class="form-control p-2 rounded-pill" id="birth_date" name="birth_date" value="<?php echo $birth_date; ?>" required>
        </div>

        <!-- Weight Field -->
        <div class="mb-3">
            <label for="weight" class="form-label">Weight (kg):</label>
            <input type="number" min="2" max="635" class="form-control p-2 rounded-pill" id="weight" name="weight" value="<?php echo $weight; ?>" required>
        </div>

        <!-- Height Field -->
        <div class="mb-3">
            <label for="height" class="form-label">Height (cm):</label>
            <input type="number" min="25" max="272" class="form-control p-2 rounded-pill" id="height" name="height" value="<?php echo $height; ?>" required>
        </div>

        <!-- Medical Condition Checkboxes -->
        <div class="mb-3">
            <label class="form-label">Medical Condition:</label>
            <div>
                <?php
                // Fetch current user data to pre-fill the form
                $query = "SELECT * FROM users WHERE username = ?";
                $stmt = $con->prepare($query);
                $stmt->bind_param("s", $username);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows > 0) {
                    $row = $result->fetch_assoc();
                    $gender = $row['gender'];
                    $birth_date = $row['birth_date'];
                    $weight = $row['weight'];
                    $height = $row['height'];
                    $medical_condition = $row['medical_condition']; // Retrieve medical condition from the database
                } else {
                    echo "User not found!";
                    exit();
                }

                $stmt->close();

                // Initialize an empty array for existing medical conditions
                $existing_conditions = [];

                // Check if $medical_condition is defined and not empty
                if (isset($medical_condition) && !empty($medical_condition)) {
                    // Split the comma-separated string into an array of existing conditions
                    $existing_conditions = explode(', ', $medical_condition);
                }

                // Determine medical condition based on BMI
                $bmi = calculateBMI($weight, $height);

                if ($bmi < 18.5) {
                    echo '<input type="checkbox" id="condition_underweight" name="medical_condition[]" value="Underweight" checked disabled>';
                    echo '<label for="condition_underweight">Underweight</label>&nbsp;';
                    // Remove 'Healthy' from existing conditions if it exists
                    $existing_conditions = array_diff($existing_conditions, ['Healthy']);
                } elseif ($bmi <= 29.9 && $bmi >= 24.9) {
                    echo '<input type="checkbox" id="condition_overweight" name="medical_condition[]" value="Overweight" checked disabled>';
                    echo '<label for="condition_overweight">Overweight</label>&nbsp;';
                    // Remove 'Healthy' from existing conditions if it exists
                    $existing_conditions = array_diff($existing_conditions, ['Healthy']);
                } elseif ($bmi >= 30) {
                    echo '<input type="checkbox" id="condition_obesity" name="medical_condition[]" value="Obesity" checked disabled>';
                    echo '<label for="condition_obesity">Obesity</label>&nbsp;';
                    // Remove 'Healthy' from existing conditions if it exists
                    $existing_conditions = array_diff($existing_conditions, ['Healthy']);
                } else {
                    echo '<input type="checkbox" id="condition_healthy" name="medical_condition[]" value="Healthy" checked disabled>';
                    echo '<label for="condition_healthy">Healthy</label>&nbsp;';
                    // Remove 'Underweight' and 'Overweight' and 'Obesity' from existing conditions if they exist
                    $existing_conditions = array_diff($existing_conditions, ['Underweight', 'Overweight', 'Obesity']);
                }

                // Check and pre-select checkboxes based on existing conditions
                $checked_cardiovascular = in_array('Cardiovascular Disease', $existing_conditions) ? 'checked' : '';
                $checked_diabetes = in_array('Diabetes', $existing_conditions) ? 'checked' : '';
                $checked_hypertension = in_array('Hypertension', $existing_conditions) ? 'checked' : '';

                // Output the checkboxes
                ?>
                <input type="checkbox" id="condition_cardiovascular" name="medical_condition[]" value="Cardiovascular Disease" <?php echo $checked_cardiovascular; ?>>
                <label for="condition_cardiovascular">Cardiovascular Disease</label><br>

                <input type="checkbox" id="condition_diabetes" name="medical_condition[]" value="Diabetes" <?php echo $checked_diabetes; ?>>
                <label for="condition_diabetes">Diabetes</label>&nbsp;

                <input type="checkbox" id="condition_hypertension" name="medical_condition[]" value="Hypertension" <?php echo $checked_hypertension; ?>>
                <label for="condition_hypertension">Hypertension</label>&nbsp;

                <!-- Medical Condition Pregnancy -->
                <div class="mb-3" id="pregnancyCheckbox" style="display: <?php echo ($gender == 'female') ? 'block' : 'none'; ?>">
                    <input type="checkbox" id="condition_pregnancy" name="medical_condition[]" value="Pregnancy" <?php echo (in_array('Pregnancy', explode(', ', $medical_condition))) ? 'checked' : ''; ?>>
                    <label for="condition_pregnancy">Pregnancy</label>
                </div>
            </div>
        </div>

        <!-- Language Selection Field -->
        <div class="mb-3">
            <label for="app_lang" class="form-label">Language:</label>
            <select class="form-select p-2 rounded-pill pe-5" id="app_lang" name="app_lang" required>
                <option value="ckb" <?php echo ($row['app_lang'] == 'ckb') ? 'selected' : ''; ?>>Kurdish</option>
                <option value="kmr" <?php echo ($row['app_lang'] == 'kmr') ? 'selected' : ''; ?>>Kurdî</option>
                <option value="ar" <?php echo ($row['app_lang'] == 'ar') ? 'selected' : ''; ?>>Arabic</option>
                <option value="en" <?php echo ($row['app_lang'] == 'en') ? 'selected' : ''; ?>>English</option>
                <option value="tr" <?php echo ($row['app_lang'] == 'tu') ? 'selected' : ''; ?>>Turkish</option>
                <option value="fa" <?php echo ($row['app_lang'] == 'fa') ? 'selected' : ''; ?>>Persian</option>
            </select>
        </div>

        <!-- Update Button -->
        <button type="submit" class="btn btn-success p-2 my-1 rounded-pill cButtonFilling" style="width: 49%;">Update</button>
        <a onclick="history.back();" class="btn btn-secondary p-2 my-1 rounded-pill cmButton" style="width: 49%;">Cancel</a>
    </form>

<!-- JavaScript for showing/hiding pregnancy checkbox -->
<script>
    $(document).ready(function () {
        $('#gender').on('change', function () {
            var selectedGender = $(this).val();
            if (selectedGender === 'female') {
                $('#pregnancyCheckbox').show();
            } else {
                $('#pregnancyCheckbox').hide();
                $('#condition_pregnancy').prop('checked', false);
            }
        });
    });
</script>
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
<script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
<script src="<?php echo url('assets/js/script.js'); ?>"></script>
</body>
</html>

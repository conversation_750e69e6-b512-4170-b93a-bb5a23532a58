<?php
// Include configuration file
require_once 'includes/config.php';

// Set the content type to JSON
header('Content-Type: application/json');

// Create the manifest array
$manifest = [
    "name" => "C-Health",
    "short_name" => "C-Health",
    "description" => "See Your Health!",
    "start_url" => BASE_URL . "/",
    "display" => "standalone",
    "background_color" => "#E0E0E0",
    "theme_color" => "#2E7D32",
    "icons" => [
        [
            "src" => BASE_URL . "/assets/pwa/192x192.png",
            "sizes" => "192x192",
            "type" => "image/png"
        ],
        [
            "src" => BASE_URL . "/assets/pwa/512x512.png",
            "sizes" => "512x512",
            "type" => "image/png"
        ]
    ],
    "screenshots" => [
        [
            "src" => BASE_URL . "/assets/pwa/1.png",
            "sizes" => "640x1136",
            "type" => "image/png",
            "label" => "Login – Access Your Health Data",
            "form_factor" => "narrow"
        ],
        [
            "src" => BASE_URL . "/assets/pwa/2.png",
            "sizes" => "640x1136",
            "type" => "image/png",
            "label" => "Dashboard – Overview of Your Health",
            "form_factor" => "narrow"
        ],
        [
            "src" => BASE_URL . "/assets/pwa/3.png",
            "sizes" => "640x1136",
            "type" => "image/png",
            "label" => "Input – Log Your Daily Activities",
            "form_factor" => "narrow"
        ],
        [
            "src" => BASE_URL . "/assets/pwa/4.png",
            "sizes" => "640x1136",
            "type" => "image/png",
            "label" => "Statistics – Detailed Health Insights",
            "form_factor" => "narrow"
        ],
        [
            "src" => BASE_URL . "/assets/pwa/5.png",
            "sizes" => "640x1136",
            "type" => "image/png",
            "label" => "Profile – Manage Your Information",
            "form_factor" => "narrow"
        ],
        [
            "src" => BASE_URL . "/assets/pwa/7.png",
            "sizes" => "1280x720",
            "type" => "image/png",
            "label" => "Overview of Login, Dashboard, and Input (Desktop)",
            "form_factor" => "wide"
        ],
        [
            "src" => BASE_URL . "/assets/pwa/8.png",
            "sizes" => "1280x720",
            "type" => "image/png",
            "label" => "Overview of Statistics, Profile, and Guide (Desktop)",
            "form_factor" => "wide"
        ]
    ],
    "version" => "1.5.8",
    "id" => "goldinpo2024",
    "scope" => BASE_URL . "/",
    "orientation" => "portrait",
    "prefer_related_applications" => false,
    "categories" => ["fitness", "food", "health", "lifestyle", "medical", "sports"],
    "launch_handler" => [
        "client_mode" => "navigate-new"
    ]
];

// Output the JSON
echo json_encode($manifest, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
?>

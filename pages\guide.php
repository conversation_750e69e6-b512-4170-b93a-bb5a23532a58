<div class="centered">
    <div class="container p-5">
        <h1 class="text-center">Guides</h1>
    <?php

        // Get the user ID from the session
        $user_id = $_SESSION['user_id'];

        // Fetch the medical condition for the user from the database
        $query = "SELECT medical_condition FROM users WHERE user_id = $user_id";
        $result = $con->query($query);

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $medical_conditions = explode(',', $row['medical_condition']);

            // Display div tags for each medical condition and corresponding content
            foreach ($medical_conditions as $condition) {
                $condition = trim($condition); // Remove leading/trailing spaces
                echo "<div class='guide$condition'></div>";

                // Show additional content based on the div class name
                switch ($condition) {
                    case 'Obesity':
                        echo '
                        <div class="Obesity">
                            <h1>Obesity</h1>
                            <p>
                                <span class="text-success">
                                Good Food: 
                                </span>
                                Whole grains (whole wheat, steel cut oats, brown rice, quinoa), Vegetables (a colorful variety-not potatoes), Whole fruits (not fruit juices), Nuts, seeds, beans, and other healthy sources of protein (fish and poultry), Plant oils (olive and other vegetable oils).
                                </p>
                                <span class="text-danger">
                                Bad Food: 
                                </span>
                                Sugar-sweetened beverages (soda, fruit drinks, sports drinks), Fruit juice (no more than a small amount per day), Refined grains(white bread, white rice, white pasta) and sweets, Potatoes (baked or fried), Red meat (beef, pork, lamb) and processed meats (salami, ham, bacon, sausage), Other highly processed foods, such as fast food
                                </p>
                            </p>
                        </div><br>
                        ';
                        break;
                    case 'Underweight':
                        echo '
                        <div class="Underweight">
                            <h1>Underweight</h1>
                            <p>
                                <span class="text-success">
                                Good Food: 
                                </span>
                                milk,banana,rice,red meat,fruit,potato,fatty fish, wheat bread ,egg,pasta,olive oil.
                                </p>
                                <span class="text-danger">
                                Bad Food: 
                                </span>
                                foods that are very high in fiber, eating more vegetable.
                                </p>
                                <ul>
                                <li>Exercise For Them</li>
                                    <ul>
                                        <li>6-17 Years: 60 Minutes 5day /W</li>
                                        <li>18-64 Years: 30-60 Minutes 5day /W</li>
                                        <li>65+ Years: 30 Minutes 5 Day/W</li>
                                    </ul>
                                    <h6>Water Intake For Every Category</h6>
                                    <h6>Men: 3.7L = 15.5 Cup Per 24 H</h6>
                                    <h6>Female: 2.7 L =11.5 Cup Per 24 H</h6>
                                </ul>
                            </p>
                        </div><br>
                        ';
                        break;
                    case 'Pregnancy':
                        echo '
                        <div class="Pregnancy">
                            <h1>Pregnancy</h1>
                            <p>
                                <span class="text-success">
                                Good Food: 
                                </span>
                                Lentil, Peas, Beans, Chickpeas, Soybeans, Peanuts ,Sweet Potatoes, Salmon, Egg, Broccoli, Kale , Lean Meat ,Berries ,Whole Grain , Avocado , Dried Fruit , Fish , Liver Oil , Spinach.
                                </p>
                                <span class="text-danger">
                                Bad Food: 
                                </span>
                                High Mercury Fish, Uncooked Fish Or Chicken And Meat , Processed Meat , Raw Egg , Organ Meat , Liver , Unpasteurized Milk , Alcohol, Soft Cheese , Fast Food, Caffeine , Smoking , Soft Drinks.
                                </p>
                                <h6>Water Intake For Every Category</h6>
                                <h6>Men: 3.7L = 15.5 Cup Per 24 H</h6>
                                <h6>Female: 2.7 L =11.5 Cup Per 24 H</h6>
                            </p>
                        </div><br>
                        ';
                        break;
                    case 'Cardiovascular Disease':
                        echo '
                        <div class="Cardiovascular">
                            <h1>Cardiovascular Disease</h1>
                            <p>
                                <span class="text-success">
                                Good Food: 
                                </span>
                                Leafy Green Vegetables, Whole Grain (Oats, Rye, Barley ,Whole Wheat ,Brown Rice, Berberine ,Avocado ,Fatty Fish ,Fish Oil ,Beans ,Tomato ,Almonds ,Chia Seeds ,Garlic ,Olive Oil ,Green Tea.
                                </p>
                                <span class="text-danger">
                                Bad Food: 
                                </span>
                                Foods high in Sodium , Frozen Vegetable,Processed Meat, Fatty Meat ,Poultry With Skin ,Butter ,Land ,Coconut, Pal Moil ,Sweet Snacks, Dessert ,Alcohol, Smoking ,Even Second Hand Smoking.
                                </p>
                                <h6>Exercise For Cardiovascular Disease</h6>
                                <h6>30minutes 5d/W</h6>
                                <br>
                                <h6>6-12 Years: 9-12 H</h6>
                                <h6>13-18 Years: 8-10 H</h6>
                                <h6>19-60 Years: 7+Hours</h6>
                                <h6>61-64 Years: 7-9 H</h6>
                                <h6>65+ Years: 7-8 H</h6>
                            </p>
                        </div><br>
                        ';
                        break;
                    case 'Diabetes':
                        echo '
                        <div class="Diabetes">
                            <h1>Diabetes</h1>
                            <p>
                                <span class="text-success">
                                Good Food: 
                                </span>
                                Vegetable, Broccoli, Carrot, Green, pepper, tomato, green, bean, orange, melon, beans, apple, banana, lean meat, chicken, turkey, nut, beans, non-fatty milk, low fatty yoghurt, egg, milk, fish, black tea, green tea, lentil, spinach, garlic.
                                </p>
                                <span class="text-danger">
                                Bad Food: 
                                </span>
                                Bread, pasta, cereal, sweet, starchy vegetables, potatoes, tea with sugar, alcohol, smoking, Food high in salt, sugar powder, soft drinking, fast food, juice, rice.
                                </p>
                                <h6>Exercise:</h6>
                                <h6>30 minutes 5d/W</h6>
                            </p>
                        </div><br>
                        ';
                        break;
                    case 'Hypertension':
                        echo '
                        <div class="Hypertension">
                            <h1>Hypertension</h1>
                            <p>
                                <span class="text-success">
                                Good Food: 
                                </span>
                                Grape, orange, lemon, salmon, nut, seeds, walnut, lentil, beans, brown rice, egg, tomato, broccoli, green pepper, onion, garlic, breast chicken, fish, all vegetable, avocado, spinach, non-fatty milk or yogurt.
                                </p>
                                <span class="text-danger">
                                Bad Food: 
                                </span>
                                Red meat, salty food, drinks high in sugar,  pizza, butter, fatty milk, cheese, fast food.
                                </p>
                                <h6>Exercise:</h6>
                                <h6>6-18 years:1 h 5d/w</h6>
                                <h6>19+ years:30 minutes 5d/w</h6>
                            </p>
                        </div><br>
                        ';
                        break;
                    case 'Healthy':
                        echo '
                        <div class="Healthy">
                            <h1 class="text-success">Healthy</h1><br>
                            <p>
                                Thank you for taking care of your health, we hope you always stay healthy. We advise you to stay healthy with the following steps:
                            </p><br>
                            <p>
                            ✔️	Base your meals on higher fibre and protein.<br>
                            ✔️	Eat lots of fruit and veg.<br>
                            ✔️	Eat more fish, including a portion of oily fish.<br>
                            ✔️	Cut down on saturated fat and sugar.<br>
                            ✔️	Eat less salt.<br>
                            ✔️	Get active and be a healthy weight.<br>
                            ✔️	Do not get thirsty.<br>
                            ✔️	Do not skip breakfast.<br>
                            ✔️	Avoid to smoking.<br>
                            ✔️	Avoid to drinking alcohol.<br>
                            </p>
                        </div><br>
                        ';
                        break;
                    // Add more cases as needed for other medical conditions
                    default:
                        // Handle cases not covered
                        break;
                }
            }
        } else {
            // Handle the case when no user with the specified ID is found
        }

        ?>
    </div>
</div>
<hr>

<?php
session_start();
require '../includes/db.php';

// Ensure the user is logged in
if (!isset($_SESSION['user_id'])) {
    echo 'You need to log in first.';
    exit();
}

$current_user_id = $_SESSION['user_id'];

if (isset($_POST['friend_username'])) {
    $friend_username = $_POST['friend_username'];

    // Prevent adding self as friend
    if ($friend_username == $_SESSION['username']) {
        echo 'You cannot add yourself as a friend.';
        exit();
    }

    // Get friend's user_id
    $stmt = $con->prepare("SELECT user_id FROM users WHERE username = ?");
    $stmt->bind_param("s", $friend_username);
    $stmt->execute();
    $stmt->store_result(); // Store the result set
    $stmt->bind_result($friend_id);
    if ($stmt->fetch()) {
        $stmt->close();

        // Check if a friend request or friendship already exists
        $check_stmt = $con->prepare("SELECT status FROM friends WHERE user_id = ? AND friend_id = ?");
        $check_stmt->bind_param("ii", $current_user_id, $friend_id);
        $check_stmt->execute();
        $check_stmt->store_result();
        $check_stmt->bind_result($status);
        if ($check_stmt->fetch()) {
            $check_stmt->close();
            if ($status == 'pending') {
                echo 'Friend request already sent.';
            } elseif ($status == 'accepted') {
                echo 'You are already friends with this user.';
            } elseif ($status == 'blocked') {
                echo 'You cannot send a friend request to this user.';
            } else {
                // Handle other statuses if any
                echo 'Cannot send friend request.';
            }
        } else {
            $check_stmt->close();

            // Insert friend request with status 'pending' and include requester_id
            $insert_stmt = $con->prepare("INSERT INTO friends (user_id, friend_id, status, requester_id) VALUES (?, ?, 'pending', ?)");
            $insert_stmt->bind_param("iii", $current_user_id, $friend_id, $current_user_id);
            if ($insert_stmt->execute()) {
                echo 'Friend request sent.';
            } else {
                echo 'Error sending friend request.';
            }
            $insert_stmt->close();
        }
    } else {
        $stmt->close();
        echo 'Username not found.';
    }
} elseif (isset($_POST['account_number'])) {
    $account_number = $_POST['account_number'];

    // Get friend's user_id
    $stmt = $con->prepare("SELECT user_id FROM users WHERE account_number = ?");
    $stmt->bind_param("s", $account_number);
    $stmt->execute();
    $stmt->store_result(); // Store the result set
    $stmt->bind_result($friend_id);
    if ($stmt->fetch()) {
        $stmt->close();

        // Prevent adding self as friend
        if ($friend_id == $current_user_id) {
            echo 'You cannot add yourself as a friend.';
            exit();
        }

        // Check if a friend request or friendship already exists
        $check_stmt = $con->prepare("SELECT status FROM friends WHERE user_id = ? AND friend_id = ?");
        $check_stmt->bind_param("ii", $current_user_id, $friend_id);
        $check_stmt->execute();
        $check_stmt->store_result();
        $check_stmt->bind_result($status);
        if ($check_stmt->fetch()) {
            $check_stmt->close();
            if ($status == 'pending') {
                echo 'Friend request already sent.';
            } elseif ($status == 'accepted') {
                echo 'You are already friends with this user.';
            } elseif ($status == 'blocked') {
                echo 'You cannot send a friend request to this user.';
            } else {
                // Handle other statuses if any
                echo 'Cannot send friend request.';
            }
        } else {
            $check_stmt->close();

            // Insert friend request with status 'pending' and include requester_id
            $insert_stmt = $con->prepare("INSERT INTO friends (user_id, friend_id, status, requester_id) VALUES (?, ?, 'pending', ?)");
            $insert_stmt->bind_param("iii", $current_user_id, $friend_id, $current_user_id);
            if ($insert_stmt->execute()) {
                echo 'Friend request sent.';
            } else {
                echo 'Error sending friend request.';
            }
            $insert_stmt->close();
        }
    } else {
        $stmt->close();
        echo 'Account number not found.';
    }
} else {
    echo 'Invalid request.';
}
?>

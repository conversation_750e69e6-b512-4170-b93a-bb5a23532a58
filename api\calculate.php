<?php
include("../includes/auth_session.php");
require_once '../includes/config.php';
require('../includes/db.php');

// Get user_id from session
$user_id = $_SESSION['user_id'];

// Retrieve the input data
$amounts = $_POST['amount'];

// Get additional inputs based on medical_condition
$diabetes = isset($_POST['diabetes']) ? (float)$_POST['diabetes'] : 0;
$bp_h = isset($_POST['bp_h']) ? (int)$_POST['bp_h'] : 0;
$bp_l = isset($_POST['bp_l']) ? (int)$_POST['bp_l'] : 0;

// Initialize totals
$total_calory = 0;
$total_carbohydrate = 0;
$total_fat = 0;
$total_sugar = 0;
$total_protein = 0;

// Process each nutrition item
foreach ($amounts as $nutrition_id => $amount) {
    $sql = "SELECT * FROM nutrition WHERE id = ?";
    $stmt = mysqli_prepare($con, $sql);

    if (!$stmt) {
        die("Error: " . mysqli_error($con));
    }

    mysqli_stmt_bind_param($stmt, "i", $nutrition_id);
    mysqli_stmt_execute($stmt);

    $result = mysqli_stmt_get_result($stmt);
    $nutrition = mysqli_fetch_assoc($result);

    // Convert the amount to a numeric type
    $amount = (float)$amount;

    // Subtract calories for items with "minute", "glass", or "hour" in "count" column
    if (strpos($nutrition['count'], 'minute') !== false ||
        strpos($nutrition['count'], 'hour') !== false ||
        strpos($nutrition['count'], 'glass') !== false) {
        $calories_to_subtract = $amount * (float)$nutrition['calory'];
        $total_calory -= $calories_to_subtract;
    } else {
        // Update totals for other items
        $total_calory += $amount * (float)$nutrition['calory'];
        $total_carbohydrate += $amount * (float)$nutrition['carbohydrate'];
        $total_fat += $amount * (float)$nutrition['fat'];
        $total_sugar += $amount * (float)$nutrition['sugar'];
        $total_protein += $amount * (float)$nutrition['protein'];
    }

    // Insert each nutrition item consumption into the food_consumption table
    $insert_consumption_sql = "INSERT INTO food_consumption (user_id, food_id, quantity, date_consumed) VALUES (?, ?, ?, ?)";
    $stmt_insert_consumption = mysqli_prepare($con, $insert_consumption_sql);

    if (!$stmt_insert_consumption) {
        die("Error preparing insert statement for food consumption: " . mysqli_error($con));
    }

    $date_consumed = date("Y-m-d");

    mysqli_stmt_bind_param($stmt_insert_consumption, "iiis", $user_id, $nutrition_id, $amount, $date_consumed);
    mysqli_stmt_execute($stmt_insert_consumption);
    mysqli_stmt_close($stmt_insert_consumption);

    mysqli_stmt_close($stmt);
}

// Get the current date
$input_date = date("Y-m-d");

// Check if there is already data for the same user and date
$select_sql = "SELECT * FROM user_nutrition WHERE user_id = ? AND input_date = ?";
$stmt_select = mysqli_prepare($con, $select_sql);

if (!$stmt_select) {
    die("Error preparing select statement: " . mysqli_error($con));
}

mysqli_stmt_bind_param($stmt_select, "is", $user_id, $input_date);
mysqli_stmt_execute($stmt_select);
$existing_data_result = mysqli_stmt_get_result($stmt_select);

if ($existing_data = mysqli_fetch_assoc($existing_data_result)) {
    // Store old data in variables
    $old_calory = $existing_data['total_calory'];
    $old_carbohydrate = $existing_data['total_carbohydrate'];
    $old_fat = $existing_data['total_fat'];
    $old_sugar = $existing_data['total_sugar'];
    $old_protein = $existing_data['total_protein'];

    // Add old data to new data
    $total_calory += $old_calory;
    $total_carbohydrate += $old_carbohydrate;
    $total_fat += $old_fat;
    $total_sugar += $old_sugar;
    $total_protein += $old_protein;
}

mysqli_stmt_close($stmt_select);

// Retrieve the user's medical condition
$query = "SELECT medical_condition FROM users WHERE user_id = ?";
$stmt = mysqli_prepare($con, $query);

if (!$stmt) {
    die("Error preparing medical condition query: " . mysqli_error($con));
}

mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);

$result = mysqli_stmt_get_result($stmt);
$user = mysqli_fetch_assoc($result);
$medical_conditions = explode(', ', $user['medical_condition']);
mysqli_stmt_close($stmt);

// Determine calorie threshold based on medical conditions
$underweight_low_calorie_threshold = 2900;
$underweight_high_calorie_threshold = 3300;
$obesity_low_calorie_threshold = 1500;
$obesity_high_calorie_threshold = 1700;
$overweight_low_calorie_threshold = 1600;
$overweight_high_calorie_threshold = 1800;

// Initialize health status
$health_status = 0;

if (in_array('Obesity', $medical_conditions)) {
    // Calculate health status for obesity
    if ($total_calory >= 2000) {
        $health_status = 0;
    } elseif ($total_calory >= $obesity_low_calorie_threshold && $total_calory <= $obesity_high_calorie_threshold) {
        $health_status = 100;
    } else {
        if ($total_calory < $obesity_low_calorie_threshold) {
            $health_status = max(0, (100 - (($obesity_low_calorie_threshold - $total_calory) / $obesity_low_calorie_threshold) * 100));
        } else {
            $health_status = max(0, (100 - (($total_calory - $obesity_high_calorie_threshold) / (2000 - $obesity_high_calorie_threshold)) * 75));
        }
    }
} elseif (in_array('Overweight', $medical_conditions)) {
    // Calculate health status for overweight
    if ($total_calory >= 2000) {
        $health_status = 0;
    } elseif ($total_calory >= $overweight_low_calorie_threshold && $total_calory <= $overweight_high_calorie_threshold) {
        $health_status = 100;
    } else {
        if ($total_calory < $overweight_low_calorie_threshold) {
            $health_status = max(0, (100 - (($overweight_low_calorie_threshold - $total_calory) / $overweight_low_calorie_threshold) * 100));
        } else {
            $health_status = max(0, (100 - (($total_calory - $overweight_high_calorie_threshold) / (2000 - $overweight_high_calorie_threshold)) * 75));
        }
    }
} elseif (in_array('Underweight', $medical_conditions)) {
    // Calculate health status for underweight
    if ($total_calory >= $underweight_low_calorie_threshold && $total_calory <= $underweight_high_calorie_threshold) {
        $health_status = 100;
    } elseif ($total_calory < $underweight_low_calorie_threshold) {
        $health_status = max(0, (100 * $total_calory / $underweight_low_calorie_threshold));
    } else {
        $health_status = max(0, (100 - (($total_calory - $underweight_high_calorie_threshold) / ($underweight_high_calorie_threshold - $underweight_low_calorie_threshold)) * 100));
    }
} else {
    // Calculate health status for healthy condition
    if ($total_calory >= 2000 && $total_calory <= 2500) {
        $health_status = 100;
    } elseif ($total_calory < 2000) {
        $health_status = max(0, (100 * $total_calory / 2000));
    } else {
        $health_status = max(0, (100 - (($total_calory - 2500) / (3000 - 2500)) * 100));
    }
}

// Update the record in the database if it exists, otherwise insert a new record
if ($existing_data) {
    $update_sql = "UPDATE user_nutrition SET total_calory = ?, total_carbohydrate = ?, total_fat = ?, total_sugar = ?, total_protein = ?, health_status = ?, diabetes = ?, bp_h = ?, bp_l = ? WHERE user_id = ? AND input_date = ?";
    $stmt_update = mysqli_prepare($con, $update_sql);

    if (!$stmt_update) {
        die("Error preparing update statement: " . mysqli_error($con));
    }

    mysqli_stmt_bind_param($stmt_update, "ddddddsddis", $total_calory, $total_carbohydrate, $total_fat, $total_sugar, $total_protein, $health_status, $diabetes, $bp_h, $bp_l, $user_id, $input_date);
    mysqli_stmt_execute($stmt_update);
    mysqli_stmt_close($stmt_update);
    } else {
        $insert_sql = "INSERT INTO user_nutrition (user_id, total_calory, total_carbohydrate, total_fat, total_sugar, total_protein, health_status, diabetes, bp_h, bp_l, input_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt_insert = mysqli_prepare($con, $insert_sql);

        if (!$stmt_insert) {
            die("Error preparing insert statement: " . mysqli_error($con));
        }

        mysqli_stmt_bind_param($stmt_insert, "idddddsddds", $user_id, $total_calory, $total_carbohydrate, $total_fat, $total_sugar, $total_protein, $health_status, $diabetes, $bp_h, $bp_l, $input_date);
        mysqli_stmt_execute($stmt_insert);
        mysqli_stmt_close($stmt_insert);
    }

    // Redirect to the dashboard
    header("Location: " . url('dashboard.php'));
    exit();

    // Close the database connection
    mysqli_close($con);
?>

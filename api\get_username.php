<?php
require('../includes/db.php');

if (isset($_POST['account_number'])) {
    $account_number = $_POST['account_number'];

    $query = "SELECT username FROM users WHERE account_number = ?";
    $stmt = $con->prepare($query);
    $stmt->bind_param("s", $account_number);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo $row['username'];
    } else {
        echo "No matching account found.";
    }
} else {
    echo "No account number provided.";
}
?>

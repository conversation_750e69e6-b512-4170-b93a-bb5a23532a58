<?php
session_start();
// Include configuration file
require_once 'includes/config.php';

// Check if the user is already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: " . url('dashboard.php'));
    exit();
}
include('components/translate_alert.php');
?>

<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C-Health | Registration</title>
    <link rel="icon" type="image/x-icon" href="<?php echo url('assets/img/fav.ico'); ?>">
    <link rel="manifest" href="<?php echo url('manifest.php'); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo url('assets/css/style-dynamic.php'); ?>"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
</head>
<body>

<?php include("components/language_selection.php"); ?>

<?php
require('includes/db.php');

// Function to check if a username already exists in the database
function isUsernameTaken($username, $con) {
    $query = "SELECT * FROM `users` WHERE username='$username'";
    $result = mysqli_query($con, $query);
    return mysqli_num_rows($result) > 0;
}

// Function to calculate BMI
function calculateBMI($weight, $height) {
    $heightInMeters = $height / 100;
    return $weight / ($heightInMeters * $heightInMeters);
}

// Function to determine medical condition based on BMI
function determineMedicalCondition($bmi) {
    if ($bmi <= 18.4) {
        return 'Underweight';
    } elseif ($bmi <= 24.9 && $bmi >=18.5) {
        return 'Overweight';
    } elseif ($bmi >= 25) {
        return 'Obesity';
    }
    // If BMI is within healthy range, return an empty string
    return '';
}

// Function to generate a unique account number
function generateUniqueAccountNumber($con) {
    do {
        $accountNumber = uniqid('ACC-', true); // Generate a unique account number
        $query = "SELECT * FROM `users` WHERE account_number='$accountNumber'";
        $result = mysqli_query($con, $query);
    } while (mysqli_num_rows($result) > 0);

    return $accountNumber;
}

if (isset($_POST['submit'])) {

    // Check if the terms checkbox is checked (add this block)
    if (!isset($_POST['acceptTerms'])) {
        echo "<script>showTranslatedAlert('You must accept the Terms and Conditions and Privacy Policy to register.'); history.back();</script>";
        exit; // Prevent form submission if the checkbox is not checked
    }

    $username = mysqli_real_escape_string($con, $_POST['username']);
    $full_name = mysqli_real_escape_string($con, $_POST['full_name']);
    $password = mysqli_real_escape_string($con, $_POST['password']);
    $email = mysqli_real_escape_string($con, $_POST['email']);
    $gender = mysqli_real_escape_string($con, $_POST['gender']);
    $birth_date = mysqli_real_escape_string($con, $_POST['birth_date']);
    $weight = mysqli_real_escape_string($con, $_POST['weight']);
    $height = mysqli_real_escape_string($con, $_POST['height']);
    $app_lang = mysqli_real_escape_string($con, $_POST['app_lang']); // Get app language

    // Hash the password before saving it to the database
    $hashed_password = password_hash($password, PASSWORD_DEFAULT); // Hashing the password

    // Calculate BMI
    $bmi = calculateBMI($weight, $height);

    // Determine medical condition based on BMI
    $medical_condition = '';
    if ($bmi >= 18.5 && $bmi < 25) {
        // If BMI is in the healthy range, only insert selected medical condition
        if (isset($_POST['medical_condition'])) {
            $medical_condition = implode(', ', $_POST['medical_condition']);
        }
    } else {
        // If BMI is outside the healthy range, determine medical condition
        $medical_condition = determineMedicalCondition($bmi);
    }

    $create_datetime = date("Y-m-d H:i:s");

    // Generate a unique account number
    $account_number = generateUniqueAccountNumber($con);

    // Check if the username is already taken
    if (isUsernameTaken($username, $con)) {
        echo "<script>showTranslatedAlert('Username is already taken. Please choose a different username.'); history.back();</script>";
    } else {
        // Insert user data and medical condition into the database
        $query = "INSERT into `users` (username, full_name, email, gender, birth_date, weight, height, medical_condition, password, bmi, create_datetime, app_lang, account_number)
        VALUES ('$username', '$full_name', '$email', '$gender', '$birth_date', '$weight', '$height', '$medical_condition', '$hashed_password', '$bmi', '$create_datetime', '$app_lang', '$account_number')";

        $result = mysqli_query($con, $query);

        if ($result) {
            echo "<script>showTranslatedAlert('You are registered successfully.'); window.location='login.php';</script>";
        } else {
            echo "<script>showTranslatedAlert('Required fields are missing or an error occurred.'); history.back();</script>";
        }
    }
} else {
?>
<?php
// Load the translations from the JSON file
$translations = json_decode(file_get_contents(__DIR__ . '/assets/lang/placeholders.json'), true);

// Get the desired language from the URL parameter
$language = isset($_GET['lang']) ? $_GET['lang'] : 'en'; // Default to 'en' if no language parameter is provided

// Function to get the translation for a given placeholder and language
function translate($placeholder, $language, $translations) {
    return isset($translations['placeholders'][$placeholder][$language]) ? $translations['placeholders'][$placeholder][$language] : $translations['placeholders'][$placeholder]['en'];
}
?>

    <form class="form p-3" action="" method="post">
    <center><img src="<?php echo url('assets/img/logo.png'); ?>" alt="logo" width="100%"></center>
        <h1 class="login-title">Registration</h1>
        <input type="text" class="form-control my-3 p-2 rounded-pill p-2" name="username" placeholder="<?php echo translate('Username', $language, $translations); ?>" required />
        <input type="text" class="form-control my-3 p-2 rounded-pill p-2" name="full_name" placeholder="<?php echo translate('Full Name', $language, $translations); ?>" required />
        <input type="password" class="form-control my-3 p-2 rounded-pill p-2" name="password" placeholder="<?php echo translate('Password', $language, $translations); ?>" required>
        <input type="email" class="form-control my-3 p-2 rounded-pill p-2" name="email" placeholder="<?php echo translate('Email Address', $language, $translations); ?>" required>
        <select class="form-control my-3 p-2 form-select select-default rounded-pill" id="gender" name="gender" placeholder="Gender" required>
            <option value="" selected disabled hidden>Gender</option>
            <option value="male">Male</option>
            <option value="female">Female</option>
        </select>
        <input class="form-control my-3 p-2 rounded-pill" type="date" id="birth_date" name="birth_date" placeholder="<?php echo translate('Birth Date', $language, $translations); ?>" required pattern="\d{4}-\d{2}-\d{2}">
        <input type="number" min="2" max="635" class="form-control my-3 p-2 rounded-pill p-2" id="weight" name="weight" placeholder="<?php echo translate('Weight', $language, $translations); ?>" required>
        <input type="number" min="25" max="272" class="form-control my-3 p-2 rounded-pill p-2" id="height" name="height" placeholder="<?php echo translate('Height', $language, $translations); ?>" required>
        <input type="hidden" name="app_lang" id="app_lang" value=""> <!-- Hidden input for app language -->

            <h6>Medical Condition</h6>
            <div class="d-none">
            <input type="checkbox" id="condition_healthy" name="medical_condition[]" value="Healthy" disabled>
            <label for="condition_healthy">Healthy</label>

            <input type="checkbox" id="condition_obesity" name="medical_condition[]" value="Obesity" disabled>
            <label for="condition_obesity">Obesity</label>
            <br>
            <input type="checkbox" id="condition_underweight" name="medical_condition[]" value="Underweight" disabled>
            <label for="condition_underweight">Underweight</label>
            </div>

            <input type="checkbox" id="condition_cardiovascular" name="medical_condition[]" value="Cardiovascular Disease">
            <label for="condition_cardiovascular">Cardiovascular Disease</label>
            <br>
            <input type="checkbox" id="condition_diabetes" name="medical_condition[]" value="Diabetes">
            <label for="condition_diabetes">Diabetes</label>

            <input type="checkbox" id="condition_hypertension" name="medical_condition[]" value="Hypertension">
            <label for="condition_hypertension">Hypertension</label>

            <div id="pregnancyCheckbox" style="display: none;">
                <input type="checkbox" id="condition_pregnancy" name="medical_condition[]" value="Pregnancy">
                <label for="condition_pregnancy">Pregnancy</label>
            </div>

            <script>
                // Function to update the href attribute of the anchor tag based on language selection
                function updateLinkUrl() {
                    const link = document.getElementById('dynamicLink');
                    const langSelect = document.getElementById('lang-select');
                    const selectedLang = langSelect.value;
                    const baseUrl = '<?php echo BASE_URL; ?>';
                    link.href = `${baseUrl}/login.php?lang=${selectedLang}`;
                    // Store selected language in local storage
                    localStorage.setItem('selectedLang', selectedLang);
                }

                // Function to retrieve the selected language from local storage
                function getSelectedLang() {
                    return localStorage.getItem('selectedLang') || 'en'; // Default to English if no selection is stored
                }

                // Call the function initially when the page loads
                document.addEventListener('DOMContentLoaded', function () {
                    const selectedLang = getSelectedLang();
                    document.getElementById('lang-select').value = selectedLang;
                    updateLinkUrl();
                });

                // Listen for changes in the language selection dropdown and update the URL accordingly
                document.getElementById('lang-select').addEventListener('change', updateLinkUrl);
            </script>


        <!-- Add the checkbox for terms and conditions and privacy policy -->
        <div class="form-check mt-3 d-flex acceptCheckbox">
            <input class="form-check-input mt-1 mx-1 custom-checkbox" type="checkbox" id="acceptTerms" name="acceptTerms" required>
            <label class="form-check-label" for="acceptTerms">
                <span>I accept the</span>
                <a href="#TermCondition" class="text-green" data-bs-toggle="modal" data-bs-target="#termConditionModal">Terms and Conditions</a>
                <span>and</span>
                <a href="#PrivacyPolicy" class="text-green" data-bs-toggle="modal" data-bs-target="#privacyPolicyModal">Privacy and Policies</a>.
            </label>
        </div>

        <button id="register" name="submit" class="form-control btn lh-1 rounded-pill p-2 mt-2 mb-1 cButtonFilling" type="submit" disabled>Register</button>
        <p class="link">Already have an account?<br><a id="dynamicLink" href="<?php echo url('login.php?lang=en'); ?>">Login here</a></p>

        <script>
            // Enable the register button only if the terms checkbox is checked
            document.getElementById('acceptTerms').addEventListener('change', function() {
                document.getElementById('register').disabled = !this.checked;
            });
        </script>

        <script>
            // Add an event listener to dynamically show/hide the pregnancy checkbox
            $(document).ready(function () {
                $('#gender').on('change', function () {
                    var selectedGender = $(this).val();
                    if (selectedGender === 'female') {
                        $('#pregnancyCheckbox').show();
                    } else {
                        $('#pregnancyCheckbox').hide();
                    }
                });
            });

            document.addEventListener('DOMContentLoaded', function() {
                var genderSelect = document.getElementById('gender');
                genderSelect.addEventListener('change', function() {
                    if (genderSelect.value === 'male') {
                        genderSelect.classList.remove('select-default');
                        genderSelect.classList.add('select-male');
                    } else if (genderSelect.value === 'female') {
                        genderSelect.classList.remove('select-default');
                        genderSelect.classList.add('select-female');
                    } else {
                        genderSelect.classList.remove('select-male', 'select-female');
                        genderSelect.classList.add('select-default');
                    }
                });

                // Set the value of the hidden input for app language
                const appLangInput = document.getElementById('app_lang');
                const htmlLang = document.documentElement.lang;
                appLangInput.value = htmlLang;
            });

            // Set the value of app_lang hidden input when the register button is clicked
            document.getElementById('register').addEventListener('click', function() {
                const appLangInput = document.getElementById('app_lang');
                const htmlLang = document.documentElement.lang;
                appLangInput.value = htmlLang;
            });
        </script>
    </form>
<?php
    }
?>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const langSelect = document.getElementById('lang-select');
        const urlParams = new URLSearchParams(window.location.search);
        const currentLang = urlParams.get('lang') || 'en'; // Default to English if no language is selected

        // Set the selected value of the dropdown based on the URL parameter
        langSelect.value = currentLang;

        // Update HTML attributes based on the selected language
        updateHtmlAttributes(currentLang);

        // Load the JSON file and update the text on the page
        loadJSON(currentLang);

        langSelect.addEventListener('change', function() {
            const selectedLang = this.value;
            urlParams.set('lang', selectedLang);
            window.location.search = urlParams.toString();
        });
    });

    function updateHtmlAttributes(lang) {
        const langDir = {
            'ckb': 'rtl',
            'kmr': 'ltr',
            'en': 'ltr',
            'ar': 'rtl',
            'fa': 'rtl',
            'tu': 'ltr'
        };

        document.documentElement.lang = lang;
        document.documentElement.dir = langDir[lang] || 'ltr';
    }

    function loadJSON(lang) {
        var baseUrl = '<?php echo BASE_URL; ?>';
        var filePath = baseUrl + '/assets/lang/' + lang + '.json';

        fetch(filePath)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                var walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                var node;
                while ((node = walker.nextNode()) !== null) {
                    var text = node.nodeValue.trim();
                    if (text !== "") {
                        if (data[text]) {
                            node.nodeValue = data[text];
                        }
                    }
                }
            })
            .catch(error => console.error('Error loading translation:', error));
    }
</script>

<div class="appVersion position-absolute top-0 start-0 px-2 p-2 text-muted">
    <?php include(__DIR__ . "/components/app_version.php"); ?>
</div>

<?php include(__DIR__ . "/pages/rules.php"); ?>

<script>
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
      var baseUrl = '<?php echo BASE_URL; ?>';
      navigator.serviceWorker.register(baseUrl + '/sw-new.js?v=1.0', {
        scope: baseUrl + '/'
      }).then(function(registration) {
        console.log('ServiceWorker registration successful with scope: ', registration.scope);
      }, function(err) {
        console.log('ServiceWorker registration failed: ', err);
      });
    });
  }
</script>

</body>
</html>

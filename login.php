<?php
session_start();
// Include configuration file
require_once 'includes/config.php';

// Check if the user is already logged in
if (isset($_SESSION['user_id'])) {
    // User is logged in, redirect to dashboard
    header("Location: " . url('dashboard.php'));
    exit();
}
ob_start();
require(__DIR__ . '/includes/db.php');
include(__DIR__ . '/components/translate_alert.php');
include(__DIR__ . '/includes/remember_me.php'); // Include the remember me logic
?>
<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C-Health | Login</title>
    <link rel="icon" type="image/x-icon" href="<?php echo url('assets/img/fav.ico'); ?>">
    <link rel="manifest" href="<?php echo url('manifest.php'); ?>">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.0/lottie.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo url('assets/css/style-dynamic.php'); ?>" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
</head>
<?php include(__DIR__ . '/components/loader.php'); ?>

<body>
    <?php
if (isset($_POST['username'])) {
    $input = trim($_POST['username']);
    $password = $_POST['password'];

    // Use prepared statements to prevent SQL injection
    if (filter_var($input, FILTER_VALIDATE_EMAIL)) {
        $query = "SELECT * FROM `users` WHERE email = ?";
    } else {
        $query = "SELECT * FROM `users` WHERE username = ?";
    }
    $stmt = $con->prepare($query);
    $stmt->bind_param("s", $input);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows === 1) {
        $user = $result->fetch_assoc();
        $storedPassword = $user['password'];

        if (password_verify($password, $storedPassword)) {
            // Password is correct
            $_SESSION['user_id'] = $user['user_id'];
            $_SESSION['username'] = $user['username'];

            // Handle Remember Me
            if (isset($_POST['remember'])) {
                // Generate selector and token
                $selector = bin2hex(random_bytes(6));
                $token = bin2hex(random_bytes(32));
                $token_hash = hash('sha256', $token);

                // Set expiration (e.g., 30 days)
                $expires = date('Y-m-d H:i:s', time() + (86400 * 30));

                // Insert token into user_tokens table
                $insertTokenQuery = "INSERT INTO user_tokens (user_id, selector, token_hash, expires) VALUES (?, ?, ?, ?)";
                $stmt = $con->prepare($insertTokenQuery);
                $stmt->bind_param("isss", $user['user_id'], $selector, $token_hash, $expires);
                $stmt->execute();

                // Set cookie with selector and token
                $cookie_value = $selector . ':' . $token;
                setcookie('remember_me', $cookie_value, [
                    'expires' => time() + (86400 * 30),
                    'path' => '/',
                    'domain' => $_SERVER['HTTP_HOST'],
                    'secure' => true,
                    'httponly' => true,
                    'samesite' => 'None'
                ]);
            }

            // Redirect to dashboard
            header("Location: " . url('dashboard.php#home'));
            exit();
        } else {
            // Password is incorrect
            echo "<script>showTranslatedAlert('Incorrect Username/password.'); window.location='" . url('login.php') . "';</script>";
        }
    } else {
        echo "<script>showTranslatedAlert('Incorrect Username/password.'); window.location='" . url('login.php') . "';</script>";
    }
} else {
?>
    <?php
$translations = json_decode(file_get_contents(__DIR__ . '/assets/lang/placeholders.json'), true);
$language = isset($_GET['lang']) ? $_GET['lang'] : 'en';

function translate($placeholder, $language, $translations) {
    return isset($translations['placeholders'][$placeholder][$language]) ? $translations['placeholders'][$placeholder][$language] : $translations['placeholders'][$placeholder]['en'];
}
?>

    <?php include(__DIR__ . "/components/language_selection.php"); ?>

    <form class="form p-3" method="post" name="login">
        <center><img src="<?php echo url('assets/img/logo.png'); ?>" alt="logo" width="100%"></center>
        <h1 class="login-title">Login</h1>
        <input type="text" class="form-control my-3 p-2 rounded-pill p-2" name="username"
            placeholder="<?php echo translate('Email or Username', $language, $translations); ?>" autofocus="true"
            required />
        <input type="password" class="form-control mt-3 p-2 rounded-pill p-2" name="password"
            placeholder="<?php echo translate('Password', $language, $translations); ?>" required />
        <div class="form-check my-2">
            <input type="checkbox" class="form-check-input align-middle custom-checkbox" id="remember" name="remember">
            <label class="form-check-label align-middle" for="remember">Remember Me</label>
        </div>
        <button type="submit" id="login" name="submit"
            class="form-control btn lh-1 rounded-pill p-2 cButtonFilling">Login</button>
        <a id="dynamicLinkTwo" class="link form-text" href="<?php echo url('forgot_password.php'); ?>">Forgot
            Password?</a>
        <div id="loginHelp" class="link form-text"><br>
            Don't have an account?<br><a id="dynamicLink"
                href="<?php echo url('registration.php?lang=en'); ?>">Registration Now</a>
        </div>
        <div class="mx-auto mt-2 w-50">
            <?php include __DIR__ . '/components/pwa-install-dialog.php'; ?>
            <!-- Include PWA Install Dialog -->
        </div>
    </form>

    <?php
}
?>

    <script>
    function updateLinkUrls() {
        const selectedLang = document.getElementById('lang-select').value;
        const baseUrl = '<?php echo BASE_URL; ?>';
        document.getElementById('dynamicLink').href = `${baseUrl}/registration.php?lang=${selectedLang}`;
        document.getElementById('dynamicLinkTwo').href = `${baseUrl}/forgot_password.php?lang=${selectedLang}`;
        localStorage.setItem('selectedLang', selectedLang);
    }

    function getSelectedLang() {
        return localStorage.getItem('selectedLang') || 'en';
    }

    document.addEventListener('DOMContentLoaded', function() {
        const selectedLang = getSelectedLang();
        document.getElementById('lang-select').value = selectedLang;
        updateLinkUrls();
    });

    document.getElementById('lang-select').addEventListener('change', updateLinkUrls);
    </script>

    <script>
    document.addEventListener("DOMContentLoaded", function() {
        const langSelect = document.getElementById('lang-select');
        const urlParams = new URLSearchParams(window.location.search);
        const currentLang = urlParams.get('lang') || 'en';

        langSelect.value = currentLang;
        updateHtmlAttributes(currentLang);
        loadJSON(currentLang);

        langSelect.addEventListener('change', function() {
            const selectedLang = this.value;
            urlParams.set('lang', selectedLang);
            window.location.search = urlParams.toString();
        });
    });

    function updateHtmlAttributes(lang) {
        const langDir = {
            'ckb': 'rtl',
            'kmr': 'ltr',
            'en': 'ltr',
            'ar': 'rtl',
            'fa': 'rtl',
            'tu': 'ltr'
        };

        document.documentElement.lang = lang;
        document.documentElement.dir = langDir[lang] || 'ltr';
    }

    function loadJSON(lang) {
        var baseUrl = '<?php echo BASE_URL; ?>';
        var filePath = baseUrl + '/assets/lang/' + lang + '.json';

        fetch(filePath)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                var walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                var node;
                while ((node = walker.nextNode()) !== null) {
                    var text = node.nodeValue.trim();
                    if (text !== "") {
                        if (data[text]) {
                            node.nodeValue = data[text];
                        }
                    }
                }
            })
            .catch(error => console.error('Error loading translation:', error));
    }
    </script>

    <div class="position-absolute bottom-0">
        <?php include(__DIR__ . "/components/footer.php"); ?>
    </div>

    <span class="appVersion position-absolute top-0 start-0 px-2 p-2 text-muted">
        <!-- <a href="dashboard.php">Dashboard</a> -->
        <?php include(__DIR__ . "/components/app_version.php"); ?>
    </span>


    <script>
    window.onload = function() {
        // Check if the modal has been shown before
        if (!localStorage.getItem('helpModalShown')) {
            var helpModal = new bootstrap.Modal(document.getElementById('helpModal'));
            helpModal.show();

            // Set the flag in localStorage to indicate the modal has been shown
            localStorage.setItem('helpModalShown', 'true');
        }
    };
    </script>

    <script>
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            var baseUrl = '<?php echo BASE_URL; ?>';
            navigator.serviceWorker.register(baseUrl + '/sw-new.js?v=1.0', {
                scope: baseUrl + '/'
            }).then(function(registration) {
                console.log('ServiceWorker registration successful with scope: ', registration.scope);
            }, function(err) {
                console.log('ServiceWorker registration failed: ', err);
            });
        });
    }
    </script>
    <script>
    document.addEventListener("DOMContentLoaded", function() {
        // Check if the URL already contains the refresh flag
        if (!window.location.search.includes('secured=true')) {
            // Add the refreshed flag to the URL and reload the page
            const newUrl = window.location.href + (window.location.search ? '&' : '?') + 'secured=true';
            window.location.href = newUrl;
        }
    });
    </script>

</body>

</html>
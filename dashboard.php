<?php
    session_start();
    ob_start();
    // Include configuration file
    require_once 'includes/config.php';
    include("includes/auth_session.php");
    require('includes/db.php');
    include ("components/nav.php");
    include('components/loader.php');
    include('components/translate_alert.php');
    $user_id = $_SESSION['user_id'];
?>
<head>
    <meta charset="utf-8">
    <link rel="stylesheet" href="<?php echo url('assets/css/style-dynamic.php'); ?>" />
    <script src="https://code.jquery.com/jquery-1.10.2.js"></script>
    <!-- Bootstrap JS and jQuery -->
    <link rel="icon" type="image/x-icon" href="<?php echo url('assets/img/fav.ico'); ?>">
    <link rel="manifest" href="<?php echo url('manifest.php'); ?>">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.0/lottie.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode-generator/1.4.4/qrcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html5-qrcode/2.3.8/html5-qrcode.min.js"></script>
</head>
<body style="position: relative;">

<!--HOME-->
    <div class="pages bgb" id="home" data-target="home" style="z-index: 9; background-color: white;">
    <?php include("pages/home.php"); ?>
    </div>

<!--STATISTICS-->
    <div class="pages bgb" id="statistics" data-target="statistics" style="background-color: white;">
    <?php include("pages/statistics.php"); ?>
    </div>

<!--INPUT-->
    <div class="pages bgb" id="input" data-target="input" style="background-color: white;">
    <?php include("pages/input.php"); ?>
    </div>

<!--GUIDE-->
    <?php
        // Fetch the user's premium status
        $query = "SELECT premium FROM users WHERE user_id = ?";
        $stmt = $con->prepare($query);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $premium_status = $row['premium']; // Get the premium status
        } else {
            // Handle case where user is not found (this should not happen since we've just fetched their details)
            echo "User not found!";
            exit();
        }
    ?>
    <div class="pages bgb" id="guide" data-target="guide" style="background-color: white;">
    <?php $ppp = 4*4+1; if ($premium_status != $ppp) {    ?>
    <div class="locked-overlay mt-5 pt-5">
            <div class="premium-content text-center mt-5 pt-5">
                <h2>Unlock Guide</h2>
                <a class="btn lh-1 rounded-pill p-2 mx-1 my-3 cButton" href="#" onclick="showTranslatedAlert('Not available at this time.'); return false;"><span class="lh-1 mx-1 bi bi-lock"> </span>Get Premium</a>
                <a class="btn restore-purchase text-muted" style="text-decoration: none;">Restore Purchase</a><br>
                <span><span>APP Version</span> <?php include("components/app_version.php"); ?></span>
            </div>
        </div>
        <?php include("pages/about.php"); ?>
    </div>
    <?php
    } else {
        // Show the guide content directly if premium is unlocked
        include("pages/guide.php");
        include("pages/about.php");
        // Optionally include other content here
    }

    ?>

<!--PROFILE/SETTING-->
    <div class="pages bgb centered p-5" id="profile" data-target="profile" style="background-color: white;">
    <?php include("pages/profile.php"); ?>
    </div>

<!-- Include Friends Modal -->
<?php include("components/friends-modal.php"); ?>


<script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
<script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
<script src="https://cdn.jsdelivr.net/npm/ionicons@latest/dist/ionicons/ionicons.js"></script>
<script src="<?php echo url('assets/js/script.js'); ?>"></script>

<script>
// Make sure the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Get all navigation list items
    const list = document.querySelectorAll(".list");

    // Function to handle active link
    function activeLink() {
        list.forEach((item) => item.classList.remove("active"));
        this.classList.add("active");
    }

    // Add click event listener to each list item
    list.forEach((item) => item.addEventListener("click", activeLink));

    // Set initial active state based on URL hash
    const hash = window.location.hash || '#home';
    const targetItem = document.querySelector(`a[href="${hash}"]`);
    if (targetItem) {
        const parentLi = targetItem.closest('li');
        if (parentLi) {
            list.forEach((item) => item.classList.remove("active"));
            parentLi.classList.add("active");
        }
    }
});

// Register service worker
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        var baseUrl = '<?php echo BASE_URL; ?>';
        navigator.serviceWorker.register(baseUrl + '/sw-new.js?v=1.0', {
            scope: baseUrl + '/'
        }).then(function(registration) {
            console.log('ServiceWorker registration successful with scope: ', registration.scope);

            // Check for updates to the service worker
            registration.addEventListener('updatefound', function() {
                // A new service worker is being installed
                const newWorker = registration.installing;
                console.log('New service worker installing:', newWorker);
            });
        }).catch(function(err) {
            console.log('ServiceWorker registration failed: ', err);
        });
    });
}

// Friends functionality
$(document).ready(function() {
    // Handle Add Friend by Username
    $('#addFriendButton').click(function() {
        var usernameToAdd = $('#usernameToAdd').val().trim();
        if (usernameToAdd !== '') {
            $.ajax({
                url: '<?php echo url('api/add_friend.php'); ?>',
                type: 'POST',
                data: { friend_username: usernameToAdd },
                success: function(response) {
                    alert(response);
                    $('#usernameToAdd').val('');
                    // Refresh the page to update friend requests count
                    location.reload();
                },
                error: function() {
                    alert('Error sending friend request.');
                }
            });
        } else {
            alert('Please enter a username.');
        }
    });

    // Generate QR Code when modal is shown
    $('#qrModal').on('shown.bs.modal', function () {
        $('#qrcode').empty();
        var qr = qrcode(0, 'M');
        qr.addData('<?php echo $account_number ?? ''; ?>');
        qr.make();
        $('#qrcode').html(qr.createImgTag(4));
    });

    // QR Code Scanner functionality
    function onScanSuccess(decodedText, decodedResult) {
        $.ajax({
            url: '<?php echo url('api/add_friend.php'); ?>',
            type: 'POST',
            data: { account_number: decodedText },
            success: function(response) {
                alert(response);
                $('#qrScannerModal').modal('hide');
                location.reload();
            },
            error: function() {
                alert('Error adding friend.');
            }
        });
    }

    // Initialize QR Code Scanner
    let html5QrCode;
    $('#qrScannerModal').on('shown.bs.modal', function () {
        html5QrCode = new Html5Qrcode("reader");
        html5QrCode.start({ facingMode: "environment" }, {
            fps: 10,
            qrbox: 250
        }, onScanSuccess).catch(err => {
            console.log(err);
        });
    });

    $('#qrScannerModal').on('hidden.bs.modal', function () {
        if (html5QrCode) {
            html5QrCode.stop().then(ignore => {
                // QR Code scanning stopped.
            }).catch(err => {
                // Stop failed, handle it.
            });
        }
    });
});

// Manage Friend Requests
function manageFriendRequest(requestId, action) {
    $.ajax({
        url: '<?php echo url('api/manage_friend_request.php'); ?>',
        type: 'POST',
        data: {
            request_id: requestId,
            action: action
        },
        success: function(response) {
            alert(response);
            location.reload();
        },
        error: function() {
            alert('Error managing friend request.');
        }
    });
}
</script>
</body>
</html>
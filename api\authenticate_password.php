<?php
session_start();
include '../includes/db.php'; // Include your database connection file

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $password = $_POST["password"];

    // Retrieve user's current password from the database
    if (isset($_SESSION['username'])) {
        $username = $_SESSION['username'];
        $query = "SELECT password FROM users WHERE username = ?";
        $stmt = $con->prepare($query);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $stmt->bind_result($stored_password);
        $stmt->fetch();
        $stmt->close();

        // Verify entered password using password_verify()
        if (password_verify($password, $stored_password)) {
            // Password is correct
            echo "success";
        } else {
            // Password is incorrect
            echo "error";
        }
    } else {
        // User not logged in
        echo "error";
    }
}
?>

<?php
// Only start a session if one doesn't already exist
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require(__DIR__ . '/db.php');
require(__DIR__ . '/config.php');

// Check if the user is not logged in
if (!isset($_SESSION['user_id'])) {
    // Check if the "remember_me" cookie exists
    if (isset($_COOKIE['remember_me'])) {
        // Extract selector and token from the cookie
        list($selector, $token) = explode(':', $_COOKIE['remember_me']);

        // Prevent SQL injection
        $selector = mysqli_real_escape_string($con, $selector);
        $token = mysqli_real_escape_string($con, $token);

        // Query the database for the token
        $query = "SELECT user_tokens.token_hash, user_tokens.expires, users.*
                  FROM user_tokens
                  INNER JOIN users ON user_tokens.user_id = users.user_id
                  WHERE user_tokens.selector = ?";
        $stmt = $con->prepare($query);
        $stmt->bind_param("s", $selector);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows === 1) {
            $row = $result->fetch_assoc();

            // Verify the token
            if (hash_equals($row['token_hash'], hash('sha256', $token))) {
                if (strtotime($row['expires']) > time()) {
                    // Token is valid and not expired
                    // Log the user in by setting session variables
                    $_SESSION['user_id'] = $row['user_id'];
                    $_SESSION['username'] = $row['username'];

                    // Optionally, regenerate session ID
                    session_regenerate_id(true);

                    // Update token expiration
                    $newExpires = date('Y-m-d H:i:s', time() + (86400 * 30));
                    $updateQuery = "UPDATE user_tokens SET expires = ? WHERE selector = ?";
                    $stmt = $con->prepare($updateQuery);
                    $stmt->bind_param("ss", $newExpires, $selector);
                    $stmt->execute();

                } else {
                    // Token expired; delete from database
                    $deleteQuery = "DELETE FROM user_tokens WHERE selector = ?";
                    $stmt = $con->prepare($deleteQuery);
                    $stmt->bind_param("s", $selector);
                    $stmt->execute();

                    // Delete the cookie
                    setcookie('remember_me', '', time() - 3600, '/');

                    // Redirect to login page
                    header('Location: ' . url('login.php'));
                    exit();
                }
            } else {
                // Invalid token; possible tampering
                // Delete all tokens for this user
                $deleteQuery = "DELETE FROM user_tokens WHERE user_id = ?";
                $stmt = $con->prepare($deleteQuery);
                $stmt->bind_param("i", $row['user_id']);
                $stmt->execute();

                // Delete the cookie
                setcookie('remember_me', '', time() - 3600, '/');

                // Redirect to login page
                header('Location: ' . url('login.php'));
                exit();
            }
        } else {
            // Token not found in database
            // Delete the cookie
            setcookie('remember_me', '', time() - 3600, '/');

            // Redirect to login page
            header('Location: ' . url('login.php'));
            exit();
        }
    } else {
        // No "remember_me" cookie; redirect to login page
        header('Location: ' . url('login.php'));
        exit();
    }
}
?>

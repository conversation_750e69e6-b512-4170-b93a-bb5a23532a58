<?php
// Include configuration file if not already included
if (!defined('BASE_URL')) {
    require_once __DIR__ . '/../includes/config.php';
}
?>
<style>
    #loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #ffffff; /* or any background color you prefer */
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 1; /* Start fully visible */
        transition: opacity 0.5s ease; /* Fade-out transition */
    }
</style>

<div id="loader">
    <div id="lottie-animation"></div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const lottieInstance = lottie.loadAnimation({
            container: document.getElementById('lottie-animation'), // The container for the animation
            renderer: 'svg',
            loop: true, // Keep the animation looping (optional)
            autoplay: true,
            path: '<?php echo BASE_URL; ?>/assets/ani/loader.json'
        });

        const loader = document.getElementById('loader');

        // When the page has fully loaded
        window.addEventListener('load', function () {
            const delay = 1000; // 1-second delay for all pages

            setTimeout(() => {
                lottieInstance.stop(); // Stop the Lottie animation
                loader.style.opacity = '0'; // Trigger fade-out
                setTimeout(() => {
                    loader.style.display = 'none'; // Remove the loader after fade-out
                }, 500); // Match the CSS transition duration
            }, delay); // Use the 1-second delay
        });
    });
</script>

# C-Health Application

C-Health is a web application that helps users monitor their health by tracking nutrition, physical activities, and health metrics.

## Folder Structure

The application is organized into the following folders:

### Root

- `index.php` - Entry point of the application
- `dashboard.php` - Main dashboard page
- `login.php` - Login page
- `logout.php` - Logout script
- `registration.php` - User registration page
- `sw.js` - Service Worker for PWA functionality
- `manifest.json` - PWA manifest file

### /api

Contains API endpoints and data processing scripts:

- `add_friend.php` - Handles friend requests
- `authenticate_password.php` - Validates user passwords
- `calculate.php` - Processes nutrition data
- `generate_pdf.php` - Generates PDF health reports
- `get_username.php` - Retrieves username information
- `manage_friend_request.php` - Manages friend request actions

### /assets

Contains static assets organized by type:

#### /assets/css

- `style.css` - Main stylesheet

#### /assets/js

- `script.js` - Main JavaScript file

#### /assets/img

- Images and icons

#### /assets/font

- Custom fonts

#### /assets/lang

- Language translation files (JSON)

#### /assets/pwa

- PWA icons and screenshots

#### /assets/ani

- Animation files

### /components

Contains reusable UI components:

- `app_version.php` - Displays app version
- `footer.php` - Footer component
- `language_selection.php` - Language selector
- `loader.php` - Loading animation
- `nav.php` - Navigation component
- `qr_code.php` - QR code generator
- `translate_alert.php` - Translation utilities

### /includes

Contains core functionality and configuration:

- `auth_session.php` - Authentication session management
- `config.php` - Application configuration and environment settings
- `db.php` - Database connection
- `remember_me.php` - Remember me functionality

### /pages

Contains main application pages:

- `about.php` - About page
- `edit_profile.php` - Profile editing page
- `edit_security.php` - Security settings page
- `home.php` - Home page content
- `input.php` - Nutrition input page
- `profile.php` - User profile page
- `rules.php` - Application rules
- `statistics.php` - Health statistics page

### /tcpdf

Third-party library for PDF generation

## Installation

1. Clone the repository
2. Set up a web server with PHP and MySQL
3. Import the database schema
4. Configure the database connection in `includes/db.php`
5. Access the application through your web server

## Deployment

The application is designed to work in both development and production environments:

### Development Environment

- The application runs at `localhost/c-health/`
- Development database credentials are used
- No configuration changes needed for local development

### Production Environment

- The application can run at the root level (e.g., `example.com/`)
- Production database credentials are automatically used
- The `includes/config.php` file handles environment detection and path adjustments

When moving from development to production:

1. Upload all files to your production server
2. No path adjustments are needed as the application automatically detects the environment
3. The database connection will automatically switch to production credentials

## Features

- User registration and authentication
- Health metrics tracking
- Nutrition logging
- Health status calculation
- PDF report generation
- Friend connections
- Multi-language support
- Progressive Web App (PWA) functionality

## Languages

The application supports multiple languages:

- English
- Arabic
- Kurdish
- Turkish
- Persian

<?php
// Get user_id from session
$user_id = $_SESSION['user_id'];

// Retrieve user data from the database
$query = "SELECT username, gender, birth_date, weight, height, bmi, medical_condition FROM users WHERE user_id = ?";
$stmt = mysqli_prepare($con, $query);

if (!$stmt) {
    die("Error: " . mysqli_error($con));
}

mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);

$result = mysqli_stmt_get_result($stmt);

if ($row = mysqli_fetch_assoc($result)) {
    $username = $row['username'];
    $gender = $row['gender'];
    $birth_date = $row['birth_date'];
    $weight = $row['weight'];
    $height = $row['height'];
    $bmi = $row['bmi'];
    $medical_condition = $row['medical_condition'];

    // Calculate age based on birth date
    $birth_date_timestamp = strtotime($birth_date);
    $current_timestamp = time();
    $age = date('Y', $current_timestamp) - date('Y', $birth_date_timestamp);

    // Provide condition based on BMI number, age, and gender
    $condition = getBMICondition($bmi, $age, $gender);

    // Display user information and BMI
    echo "<div class='text-center mt-1 mb-2'>";
    echo "<h6 class='d-inline'>Your BMI is <b>$bmi</b></h6>";
    echo "<h6 class='d-inline'>, You are <b>$condition</b>.</h6>";
    echo "</div>";
} else {
    echo "User not found.";
}

// Close the statement
mysqli_stmt_close($stmt);

// Function to get BMI condition based on BMI number, age, and gender
function getBMICondition($bmi, $age, $gender)
{
    // Define BMI categories and their ranges
    $categories = array(
        'Underweight' => array('min' => 0, 'max' => 18.5),
        'Normal Weight' => array('min' => 18.5, 'max' => 24.9),
        'Overweight' => array('min' => 25, 'max' => 29.9),
        'Obese' => array('min' => 30, 'max' => 100),
    );

    // Adjust BMI categories based on age and gender if necessary
    if ($gender === 'male' && $age > 50) {
        $categories['Normal Weight']['max'] += 1; // Adjust for older males
        $categories['Overweight']['max'] += 1; // Adjust for older males
        $categories['Obese']['max'] += 1; // Adjust for older males
    } elseif ($gender === 'female' && $age > 50) {
        $categories['Normal Weight']['max'] += 1.5; // Adjust for older females
        $categories['Overweight']['max'] += 1.5; // Adjust for older females
        $categories['Obese']['max'] += 1.5; // Adjust for older females
    }

    // Determine the BMI condition based on the calculated BMI
    foreach ($categories as $condition => $range) {
        if ($bmi >= $range['min'] && $bmi <= $range['max']) {
            return $condition;
        }
    }

    return 'Undefined';
}


if ($row) {
    $weight = $row['weight'];
    $height = $row['height'] / 100; // Convert height to meters if stored in centimeters
    $bmi = $row['bmi'];
    $gender = $row['gender']; // Assuming you have gender data in the row

    // Calculate the ideal weight range (based on a normal BMI range of 18.5 - 24.9)
    $min_ideal_weight = 18.5 * ($height * $height);
    $max_ideal_weight = 24.9 * ($height * $height);

    // Use the getBMICondition function to get the current condition
    $medical_condition = getBMICondition($bmi, $age, $gender);

    echo "<div class='text-center mt-1 mb-2'>";

    if ($medical_condition === 'Overweight' || $medical_condition === 'Obesity' || $bmi >=24.9) {
        $excess_weight = $weight - $max_ideal_weight;
        if ($excess_weight > 0) {
            echo "<p><span>You are OVER your ideal weight by </span> <b>" . round($excess_weight, 2) . " <span>kg</span></b>.</p>";
        }
    } elseif ($medical_condition === 'Underweight' || $bmi <=18.5) {
        $needed_weight = $min_ideal_weight - $weight;
        if ($needed_weight > 0) {
            echo "<p><span>You are UNDER your ideal weight by </span> <b>" . round($needed_weight, 2) . " <span>kg</span></b>.</p>";
        }
    } elseif ($medical_condition === 'Healthy' || ($bmi <=24.9 && $bmi >=18.5)) {
        echo "<p>Your weight is <b>normal</b> 🎉</p>";
    } else {
        echo "<p>Unable to determine your weight condition.</p>";
    }

    echo "</div>";
} else {
    echo "<p>Unable to retrieve user data.</p>";
}


// Get the current date
$current_date = date("Y-m-d");
// Calculate the date for one week ago
$one_week_ago = date("Y-m-d", strtotime("-1 week"));

// Retrieve and display nutrition data for the past seven days, sorted by newest date
$sql = "SELECT DISTINCT input_date FROM user_nutrition WHERE user_id = ? AND input_date BETWEEN ? AND ? ORDER BY input_date DESC";
$stmt = mysqli_prepare($con, $sql);

if (!$stmt) {
    die("Error: " . mysqli_error($con));
}

mysqli_stmt_bind_param($stmt, "iss", $user_id, $one_week_ago, $current_date);
mysqli_stmt_execute($stmt);

$result = mysqli_stmt_get_result($stmt);

echo "<center><h4>Nutrition Data for the Past Week</h4></center>";

// Existing code where you fetch data for each date
while ($row = mysqli_fetch_assoc($result)) {
    $input_date = $row['input_date'];

    // Fetch data for the specific date
    $sql_data = "SELECT * FROM user_nutrition WHERE user_id = ? AND input_date = ? ORDER BY input_date DESC LIMIT 1";
    $stmt_data = mysqli_prepare($con, $sql_data);

    if (!$stmt_data) {
        die("Error: " . mysqli_error($con));
    }

    mysqli_stmt_bind_param($stmt_data, "is", $user_id, $input_date);
    mysqli_stmt_execute($stmt_data);

    $result_data = mysqli_stmt_get_result($stmt_data);

    // Fetch and display data for the unique date
    while ($row_data = mysqli_fetch_assoc($result_data)) {
        $total_calory = round($row_data['total_calory']);
        $total_carbohydrate = round($row_data['total_carbohydrate']);
        $total_fat = round($row_data['total_fat']);
        $total_sugar = round($row_data['total_sugar']);
        $total_protein = round($row_data['total_protein']);
        $average_score = $row_data['health_status'];
        $diabetes = $row_data['diabetes'];
        $bp_h = $row_data['bp_h'];
        $bp_l = $row_data['bp_l'];
        // ...

        // Fetch daily food consumption data for this date
        $sql_daily_data = "
            SELECT
                n.nutrition AS food_name,
                SUM(fc.quantity) as total_quantity,
                n.count
            FROM
                food_consumption fc
            JOIN
                nutrition n ON fc.food_id = n.id
            WHERE
                fc.user_id = ? AND fc.date_consumed = ?
            GROUP BY
                n.nutrition, n.count
            ORDER BY
                n.filter;
        ";

        $stmt_daily_data = mysqli_prepare($con, $sql_daily_data);
        if (!$stmt_daily_data) {
            die("Error: " . mysqli_error($con));
        }
        mysqli_stmt_bind_param($stmt_daily_data, "is", $user_id, $input_date);
        mysqli_stmt_execute($stmt_daily_data);
        $result_daily_data = mysqli_stmt_get_result($stmt_daily_data);

        // Fetch daily data into an array
        $daily_data = [];
        while ($row_daily = mysqli_fetch_assoc($result_daily_data)) {
            $daily_data[] = $row_daily;
        }
        mysqli_stmt_close($stmt_daily_data);

        // Generate a unique modal ID per date (e.g., dailyModal_2023_10_05)
        $formatted_date = date("Y-m-d", strtotime($input_date));
        $modal_id = 'dailyModal_' . str_replace('-', '_', $formatted_date);

        // Generate the button to open the modal
        echo "<center>";
        echo "<details class='nutrition-details p-1'>";
        echo "<summary style='width: auto; background-color: lightgray;'>
                <p class='d-inline'>Date: </p>$formatted_date
                <button type='button' class='mx-1 px-1 btn btn-secondary rounded-0' data-bs-toggle='modal' data-bs-target='#$modal_id'>
                    <p class='d-inline'>&nbsp;</p>
                    <i class='bi bi-calendar-event align-middle'></i>
                    <p class='d-inline'>&nbsp; Consumption &nbsp;</p>
                </button>
            </summary>";
            echo "<div style='display: flex; flex-wrap: wrap;'>";
            echo "<ul class='list-inline'>";
            echo "<li class='list-inline-item m-3'><span class='centered p-1 liStats' style='width: 50px; height: 50px; border-radius: 50%; background-color: #2E7D32; color: #fff;'>$total_calory</span>Calories</li>";
            echo "<li class='list-inline-item m-3'><span class='centered p-1 liStats' style='width: 50px; height: 50px; border-radius: 50%; background-color: #2E7D32; color: #fff;'>$total_carbohydrate</span>Carbs</li>";
            echo "<li class='list-inline-item m-3'><span class='centered p-1 liStats' style='width: 50px; height: 50px; border-radius: 50%; background-color: #2E7D32; color: #fff;'>$total_fat</span>Fat</li>";
            echo "<li class='list-inline-item m-3'><span class='centered p-1 liStats' style='width: 50px; height: 50px; border-radius: 50%; background-color: #2E7D32; color: #fff;'>$total_sugar</span>Sugar</li>";
            echo "<li class='list-inline-item m-3'><span class='centered p-1 liStats' style='width: 50px; height: 50px; border-radius: 50%; background-color: #2E7D32; color: #fff;'>$total_protein</span>Protein</li>";
            echo "<li class='list-inline-item m-3'><span class='centered p-1 liStats' style='width: 50px; height: 50px; border-radius: 50%; background-color: #2E7D32; color: #fff;'>%$average_score</span>Score</li>";

            // Check if diabetes and hypertension are not zero before displaying
            if ($diabetes != 0) {
                echo "<li class='list-inline-item m-3'><span class='centered p-1 liStats' style='width: 50px; height: 50px; border-radius: 50%; background-color: #2E7D32; color: #fff;'>$diabetes</span>DM<sub style='font-size: 10px;'>(mmol/L)</sub></li>";
            }

            if ($bp_h != 0 && $bp_l != 0) {
                echo "<li class='list-inline-item m-3'><span class='centered p-1 liStats' style='width: 50px; height: 50px; border-radius: 50%; background-color: #2E7D32; color: #fff; font-size: 13px;'>$bp_h/$bp_l</span>BP<sub style='font-size: 10px;'>(mmHg)</sub></li>";
            }

            echo "</ul>";
            echo "</div>";
        // ...
        echo "</details>";
        echo "</center>";

        // Generate the modal for this date
        echo "
        <!-- Modal for Daily Consumption for date $formatted_date -->
        <div class=\"modal fade dailyConMod\" id=\"$modal_id\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"dailyModalLabel_$modal_id\" aria-hidden=\"true\">
            <div class=\"modal-dialog\" role=\"document\">
                <div class=\"modal-content rounded-0\">
                    <div class=\"modal-header px-3\">
                        <h5 class=\"modal-title pb-2 text-center\" id=\"dailyModalLabel_$modal_id\"><span>Nutrition Consumption for</span> $formatted_date</h5>
                    </div>
                    <div class=\"modal-body2 p-3 px-3\">
                        <table class=\"table\">
                            <thead>
                                <tr>
                                    <th>Nutrition</th>
                                    <th>Quantity</th>
                                    <th>Count</th>
                                </tr>
                            </thead>
                            <tbody>";
        // Populate the table rows with daily data
        foreach ($daily_data as $data) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($data['food_name']) . "</td>";
            echo "<td class='text-center'>" . round($data['total_quantity']) . "</td>";
            echo "<td>" . htmlspecialchars($data['count']) . "</td>";
            echo "</tr>";
        }
        echo "          </tbody>
                        </table>
                    </div>
                    <div class=\"modal-footer d-flex justify-content-center py-2\">
                        <button type=\"button\" class=\"btn btn-secondary rounded-pill p-1 px-2 mx-2\" data-bs-dismiss=\"modal\">Close</button>
                    </div>
                </div>
            </div>
        </div>
        ";

        // Close the statement for the daily data
        mysqli_stmt_close($stmt_data);
    }
}

?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get all details tags
    var detailsTags = document.querySelectorAll('.nutrition-details');

    // Add click event listener to each details tag
    detailsTags.forEach(function(detailsTag) {
        detailsTag.addEventListener('click', function() {
            // Close all other details tags
            detailsTags.forEach(function(otherDetailsTag) {
                if (otherDetailsTag !== detailsTag) {
                    otherDetailsTag.removeAttribute('open');
                }
            });
        });
    });
});
</script>

<center class="mb-5">
    <!-- Button to trigger modal -->
    <button type="button" class="btn btn-primary mt-3 p-2 rounded-pill" data-bs-toggle="modal" data-bs-target="#nutritionModal">
        <i class="bi bi-calendar-week align-middle"></i><p class="d-inline">&nbsp;</p>Last Week Health
    </button>

    <!-- New Button to trigger modal for Lifetime Consumption -->
    <button type="button" class="btn btn-secondary mt-3 p-2 rounded-pill" data-bs-toggle="modal" data-bs-target="#lifetimeModal">
        <i class="bi bi-calendar align-middle"></i><p class="d-inline">&nbsp;</p>Lifetime Consumption
    </button>
</center>

<?php
// Retrieve lifetime food consumption data
$sql_lifetime = "
    SELECT
        n.nutrition AS food_name,
        SUM(fc.quantity) as total_quantity,
        n.count
    FROM
        food_consumption fc
    JOIN
        nutrition n ON fc.food_id = n.id
    WHERE
        fc.user_id = ?
    GROUP BY
        n.nutrition, n.count
    ORDER BY
        n.filter;
";

$stmt_lifetime = mysqli_prepare($con, $sql_lifetime);

if (!$stmt_lifetime) {
    die("Error: " . mysqli_error($con));
}

mysqli_stmt_bind_param($stmt_lifetime, "i", $user_id);
mysqli_stmt_execute($stmt_lifetime);

$result_lifetime = mysqli_stmt_get_result($stmt_lifetime);

$lifetime_data = [];
while ($row = mysqli_fetch_assoc($result_lifetime)) {
    $lifetime_data[] = $row;
}

mysqli_stmt_close($stmt_lifetime);
?>

<!-- Modal for Lifetime Consumption -->
<div class="modal fade" id="lifetimeModal" tabindex="-1" role="dialog" aria-labelledby="lifetimeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content rounded-0">
            <div class="modal-header px-3">
                <h5 class="modal-title pb-2 text-center" id="lifetimeModalLabel">Lifetime Nutrition Consumption</h5>
            </div>
            <div class="modal-body2 p-3 px-3">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Nutrition</th>
                            <th>Quantity</th>
                            <th>Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Iterate through lifetime data and create table rows
                        foreach ($lifetime_data as $data) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($data['food_name']) . "</td>";
                            echo "<td class='text-center'>" . round($data['total_quantity']) . "</td>";
                            echo "<td>" . htmlspecialchars($data['count']) . "</td>";
                            echo "</tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
            <div class="modal-footer d-flex justify-content-center py-2">
                <button type="button" class="btn btn-secondary rounded-pill p-1 px-2 mx-2" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php
// Check if username is set in the session
if (isset($_SESSION['username'])) {
    $username = $_SESSION['username'];
} else {
    // Handle the case where the username is not set
    $username = 'User';  // Default value or handle error appropriately
}
?>

<script>
// Make the PHP username available in JavaScript
const username = <?php echo json_encode($username); ?>;
</script>

    <!-- Modal -->
    <div class="modal fade" id="nutritionModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content rounded-0">
                <div id="nutritionTable" class="modal-body p-3">
                    <div class="modal-header px-3">
                        <h5 class="modal-title pb-2" id="exampleModalLabel">Last Week Health</h5>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>DM (mmol/L)</th>
                                <th>BP (mmHg)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Get user_id from session
                            $user_id = $_SESSION['user_id'];

                            // Get the current date
                            $current_date = date("Y-m-d");
                            // Calculate the date for one week ago
                            $one_week_ago = date("Y-m-d", strtotime("-1 week"));

                            // Retrieve and display nutrition data for the past seven days, sorted by newest date
                            $sql = "SELECT input_date, diabetes, bp_h, bp_l FROM user_nutrition WHERE user_id = ? AND input_date BETWEEN ? AND ? ORDER BY input_date DESC";
                            $stmt = mysqli_prepare($con, $sql);

                            if (!$stmt) {
                                die("Error: " . mysqli_error($con));
                            }

                            mysqli_stmt_bind_param($stmt, "iss", $user_id, $one_week_ago, $current_date);
                            mysqli_stmt_execute($stmt);

                            $result = mysqli_stmt_get_result($stmt);
                            // Fetch and display nutrition data for the past week
                            while ($row = mysqli_fetch_assoc($result)) {
                                $input_date = date("Y-m-d", strtotime($row['input_date']));
                                $diabetes = $row['diabetes'];
                                $bp_h = $row['bp_h'];
                                $bp_l = $row['bp_l'];
                            ?>
                                <tr>
                                    <td><?php echo $input_date; ?></td>
                                    <td><?php echo $diabetes; ?></td>
                                    <td><?php echo "$bp_h / $bp_l"; ?></td>
                                </tr>
                            <?php
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer d-flex justify-content-center py-2">
                    <button type="button" class="btn btn-primary rounded-pill p-1 px-2 mx-2" onclick="saveAsPDF()"><i class="bi bi-floppy align-middle"></i> PDF</button>
                    <button type="button" class="btn btn-primary rounded-pill p-1 px-2 mx-2" onclick="saveAsImage()"><i class="bi bi-floppy align-middle"></i> IMG</button>
                    <button type="button" class="btn btn-secondary rounded-pill p-1 px-2 mx-2" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</center>

<script>
function saveAsImage() {
    html2canvas(document.querySelector("#nutritionTable")).then(canvas => {
        // Get current date and time
        const now = new Date();

        // Format the date to dd-mm-yyyy
        const date = now.getDate().toString().padStart(2, '0') + '-' +
                    (now.getMonth() + 1).toString().padStart(2, '0') + '-' +
                    now.getFullYear();

        // Format the time to hh-mm-ss
        const time = now.getHours().toString().padStart(2, '0') + '-' +
                    now.getMinutes().toString().padStart(2, '0') + '-' +
                    now.getSeconds().toString().padStart(2, '0');

        // Create a filename with username, date, and time
        const filename = username + '_health_report_' + date + '_' + time + '.png';

        // Create a temporary link element
        const link = document.createElement('a');
        link.href = canvas.toDataURL('image/png');
        link.download = filename;

        // Trigger the download
        link.click();
    });
}

function saveAsPDF() {
    // Redirect to the generate_pdf.php script
    window.location.href = '/api/generate_pdf.php';
}
</script>

<?php
include("../includes/auth_session.php");
require('../includes/db.php');
require_once '../tcpdf/tcpdf.php'; // Include TCPDF library

// Initialize variables
$username = "";
$full_name = "";
$gender = "";
$birth_date = "";
$weight = "";
$height = "";
$age = "";
$bmi = "";
$condition = "";
$medical_condition = "";
$html = ''; // Initialize $html variable

// Get user_id from session
$user_id = $_SESSION['user_id'];

// Retrieve user data from the database
$query = "SELECT username, full_name, gender, birth_date, weight, height, medical_condition FROM users WHERE user_id = ?";
$stmt = mysqli_prepare($con, $query);

if (!$stmt) {
    die("Error: " . mysqli_error($con));
}

mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);

$result = mysqli_stmt_get_result($stmt);

if ($row = mysqli_fetch_assoc($result)) {
    $username = $row['username'];
    $full_name = $row['full_name'];
    $gender = $row['gender'];
    $birth_date = $row['birth_date'];
    $weight = $row['weight'];
    $height = $row['height'];
    $medical_condition = $row['medical_condition'];

    // Calculate age based on birth date
    $birth_date_timestamp = strtotime($birth_date);
    $current_timestamp = time();
    $age = date('Y', $current_timestamp) - date('Y', $birth_date_timestamp);

    // Provide condition based on BMI number, age, and gender
    $condition = getBMICondition($age, $gender, $weight, $height);
} else {
    echo "User not found.";
    exit;
}

mysqli_stmt_close($stmt);

// Function to get BMI condition based on age, gender, weight, and height
function getBMICondition($age, $gender, $weight, $height)
{
    // Calculate BMI
    $bmi = $weight / (($height / 100) * ($height / 100));

    // Define BMI categories and their ranges
    $categories = array(
        'Underweight' => array('min' => 0, 'max' => 18.5),
        'Normal Weight' => array('min' => 18.5, 'max' => 24.9),
        'Overweight' => array('min' => 25, 'max' => 29.9),
        'Obese' => array('min' => 30, 'max' => 100),
    );

    // Adjust BMI categories based on age and gender if necessary
    if ($gender === 'male' && $age > 50) {
        $categories['Normal Weight']['max'] += 1; // Adjust for older males
        $categories['Overweight']['max'] += 1; // Adjust for older males
        $categories['Obese']['max'] += 1; // Adjust for older males
    } elseif ($gender === 'female' && $age > 50) {
        $categories['Normal Weight']['max'] += 1.5; // Adjust for older females
        $categories['Overweight']['max'] += 1.5; // Adjust for older females
        $categories['Obese']['max'] += 1.5; // Adjust for older females
    }

    // Determine the BMI condition based on the calculated BMI
    foreach ($categories as $condition => $range) {
        if ($bmi >= $range['min'] && $bmi <= $range['max']) {
            return $condition;
        }
    }

    return 'Undefined';
}

// Fetch nutrition data for the past week
$nutrition_html = '';
$sql = "SELECT input_date, diabetes, bp_h, bp_l FROM user_nutrition WHERE user_id = ? AND input_date BETWEEN DATE_SUB(NOW(), INTERVAL 1 WEEK) AND NOW() ORDER BY input_date DESC";
$stmt = mysqli_prepare($con, $sql);

if (!$stmt) {
    die("Error: " . mysqli_error($con));
}

mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);

$result = mysqli_stmt_get_result($stmt);

while ($row = mysqli_fetch_assoc($result)) {
    $input_date = date("Y-m-d", strtotime($row['input_date']));
    $diabetes = $row['diabetes'];
    $bp_h = $row['bp_h'];
    $bp_l = $row['bp_l'];

    $nutrition_html .= "<tr>";
    $nutrition_html .= "<td style=\"border: 1px solid #000;\"> $input_date</td>";
    $nutrition_html .= "<td style=\"border: 1px solid #000;\"> $diabetes</td>";
    $nutrition_html .= "<td style=\"border: 1px solid #000;\"> $bp_h / $bp_l</td>";
    $nutrition_html .= "</tr>";
}

mysqli_stmt_close($stmt);

date_default_timezone_set('Asia/Baghdad');
// Create new PDF document

class MYPDF extends TCPDF {
    // Add this line to declare the username property
    public $username;

    // Page footer
    public function Footer() {
        // Position at 15 mm from bottom
        $this->SetY(-15);

        // Set font
        $this->SetFont('helvetica', 'I', 8);

        // Add current date and time along with the username to the left of the footer
        $this->Cell(0, 10, 'Generated on ' . date('Y-m-d H:i:s') . ' for @' . $this->username, 0, false, 'L', 0, '', 0, false, 'T', 'M');

        // Add website URL to the right of the footer
        $this->Cell(0, 10, 'www.c-health.net', 0, false, 'R', 0, '', 0, false, 'T', 'M');
    }
}


$pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

$pdf->username = $username;
// Set document information
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('C-Health');
$pdf->SetTitle('Health Report ' . $username . ' ' . date('dmY His')); // Include username, date, and time in the file name
$pdf->SetSubject('Last Week Health Report');
$pdf->SetKeywords('Health, Report, PDF');

// Remove default header
$pdf->setPrintHeader(false);
$pdf->setPrintFooter(true);

// Set margins
$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
$pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
$pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

// Set auto page breaks
$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

// Set image scale factor
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

// Set font
$pdf->SetFont('helvetica', '', 12);

// Add a page
$pdf->AddPage();

// Output the logo at the top of the page
$logo_path = '../assets/img/logo.png';
if (file_exists($logo_path)) {
    $pdf->Image($logo_path, 15, 10, 50, '', 'PNG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->Ln(17); // Move the cursor down after the logo to avoid overlapping text
} else {
    echo "Logo file not found.";
    exit;
}

// Add "C-Health Application" text and a horizontal line
$pdf->SetFont('helvetica', 'B', 16);
$pdf->Cell(0, 15, 'C-Health Application', 0, 1, 'C');
$pdf->Ln(5); // Add some space between the text and the line
$pdf->SetLineWidth(0.5);
$pdf->Line(10, $pdf->GetY(), 200, $pdf->GetY());
$pdf->Ln(10); // Add some space after the line

// Set some content to display with custom line height
$html .= "<h2>User's information</h2>";
$html .= "<p><b>Full Name:</b> $full_name</p>";
$html .= "<p><b>Gender:</b> $gender</p>";
$html .= "<p><b>Age:</b> $age</p>";
$html .= "<p><b>Weight:</b> $weight kg</p>";
$html .= "<p><b>Height:</b> $height cm</p>";
$html .= "<p><b>Condition:</b> $condition</p>";
$html .= "<p><b>Medical Condition:</b> $medical_condition</p>";

// Add nutrition data to the content
$html .= "<div class='modal-body p-3'>";
$html .= "<h2>Last Week Health</h2>";
$html .= "<table class='table' style='border-collapse: collapse; width: 100%;'>";
$html .= "<thead>";
$html .= "<tr>";
$html .= "<th style=\"border: 1px solid #000;\"> Date</th>";
$html .= "<th style=\"border: 1px solid #000;\"> DM (mmol/L)</th>";
$html .= "<th style=\"border: 1px solid #000;\"> BP (mmHg)</th>";
$html .= "</tr>";
$html .= "</thead>";
$html .= "<tbody>";
$html .= $nutrition_html;
$html .= "</tbody>";
$html .= "</table>";
$html .= "</div>";

// Output the HTML content
$pdf->writeHTML($html, true, false, true, false, '');

// Format current date and time
$current_date_time = date('d-m-Y H:i:s'); // Format: dd-mm-yyyy hh:mm

// Generate output file name
$output_file_name = "C-Health Report $username $current_date_time.pdf";

// Close and output PDF document with the new file name
$pdf->Output($output_file_name, 'D');
?>

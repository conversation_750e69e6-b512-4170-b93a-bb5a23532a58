<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/db.php';
include('components/translate_alert.php');

if (isset($_GET['token'])) {
    $token = $_GET['token'];

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Retrieve and trim the user inputs
        $password = trim($_POST['password']);
        $confirm_password = trim($_POST['confirm_password']);

        if ($password !== $confirm_password) {
            echo "<script>showTranslatedAlert('Passwords do not match.');</script>";
        } else {
            // Hash the new password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // Prepare and execute the query to select the user with the provided token
            $query = "SELECT * FROM `users` WHERE reset_token = ? AND token_expiry > ?";
            $stmt = $con->prepare($query);
            $currentTime = time();
            $stmt->bind_param("si", $token, $currentTime);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows == 1) {
                // Update the user's password and clear the reset token
                $update_query = "UPDATE `users` SET password = ?, reset_token = NULL, token_expiry = NULL WHERE reset_token = ?";
                $stmt_update = $con->prepare($update_query);
                $stmt_update->bind_param("ss", $hashed_password, $token);

                if ($stmt_update->execute()) {
                    echo "<script>showTranslatedAlert('Your password has been successfully reset.'); window.location.href = '" . url('login.php') . "';</script>";
                    exit;
                } else {
                    echo "<script>showTranslatedAlert('Error updating password. Please try again.');</script>";
                }
                $stmt_update->close();
            } else {
                echo "<script>showTranslatedAlert('The token is invalid or has expired.'); window.location.href = '" . url('forgot_password.php') . "';</script>";
                exit;
            }
            $stmt->close();
        }
    }
} else {
    echo "<script>showTranslatedAlert('Invalid or missing token.'); window.location.href = '" . url('forgot_password.php') . "';</script>";
    exit;
}
?>
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Reset Password</title>
    <link rel="icon" type="image/x-icon" href="<?php echo url('assets/img/fav.ico'); ?>">
    <link rel="manifest" href="<?php echo url('manifest.php'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Bootstrap CSS and other styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo url('assets/css/style-dynamic.php'); ?>"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
</head>
<body>

<?php
    // Load the translations from the JSON file
    $translations = json_decode(file_get_contents(__DIR__ . '/assets/lang/placeholders.json'), true);

    // Get the desired language from the URL parameter
    $language = isset($_GET['lang']) ? $_GET['lang'] : 'en'; // Default to 'en' if no language parameter is provided

    // Function to get the translation for a given placeholder and language
    function translate($placeholder, $language, $translations) {
        return isset($translations['placeholders'][$placeholder][$language]) ? $translations['placeholders'][$placeholder][$language] : $translations['placeholders'][$placeholder]['en'];
    }
?>

<?php include("components/language_selection.php"); ?>

<form class="form p-3" method="post" action="" onsubmit="return validatePasswords()">
    <center><img src="<?php echo url('assets/img/logo.png'); ?>" alt="logo" width="100%"></center>
    <h1 class="login-title">Reset Password</h1>
    <input type="password" class="form-control my-3 p-2 rounded-pill" id="password" name="password" placeholder="<?php echo translate('New Password', $language, $translations); ?>" required>
    <input type="password" class="form-control my-3 p-2 rounded-pill" id="confirm_password" name="confirm_password" placeholder="<?php echo translate('Confirm Password', $language, $translations); ?>" required>
    <button type="submit" class="form-control btn btn-success p-2 rounded-pill cButtonFilling">Reset Password</button>
</form>

<!-- Password validation script -->
<script>
    function validatePasswords() {
        var password = document.getElementById("password").value;
        var confirm_password = document.getElementById("confirm_password").value;
        if (password !== confirm_password) {
            showTranslatedAlert("Passwords do not match.");
            return false;
        }
        return true;
    }
</script>

<!-- Language selection script -->
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const langSelect = document.getElementById('lang-select');
        const urlParams = new URLSearchParams(window.location.search);
        const currentLang = urlParams.get('lang') || 'en'; // Default to English if no language is selected

        // Set the selected value of the dropdown based on the URL parameter
        langSelect.value = currentLang;

        // Update HTML attributes based on the selected language
        updateHtmlAttributes(currentLang);

        // Load the JSON file and update the text on the page
        loadJSON(currentLang);

        langSelect.addEventListener('change', function() {
            const selectedLang = this.value;
            urlParams.set('lang', selectedLang);
            window.location.search = urlParams.toString();
        });
    });

    function updateHtmlAttributes(lang) {
        const langDir = {
            'ckb': 'rtl',
            'kmr': 'ltr',
            'en': 'ltr',
            'ar': 'rtl',
            'fa': 'rtl',
            'tr': 'ltr'
        };

        document.documentElement.lang = lang;
        document.documentElement.dir = langDir[lang] || 'ltr';
    }

    function loadJSON(lang) {
        var baseUrl = '<?php echo BASE_URL; ?>';
        var filePath = baseUrl + '/assets/lang/' + lang + '.json';

        fetch(filePath)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                var walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                var node;
                while ((node = walker.nextNode()) !== null) {
                    var text = node.nodeValue.trim();
                    if (text !== "") {
                        if (data[text]) {
                            node.nodeValue = data[text];
                        }
                    }
                }
            })
            .catch(error => console.error('Error loading translation:', error));
    }
</script>

<!-- Bootstrap JS and other scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>

<div class="appVersion position-absolute top-0 start-0 px-2 p-2 text-muted">
    <?php include(__DIR__ . "/components/app_version.php"); ?>
</div>

<script>
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
      var baseUrl = '<?php echo BASE_URL; ?>';
      navigator.serviceWorker.register(baseUrl + '/sw-new.js?v=1.0', {
        scope: baseUrl + '/'
      }).then(function(registration) {
        console.log('ServiceWorker registration successful with scope: ', registration.scope);
      }, function(err) {
        console.log('ServiceWorker registration failed: ', err);
      });
    });
  }
</script>

</body>
</html>

<?php
include("../includes/auth_session.php");
require_once '../includes/config.php';
require('../includes/db.php');
include('../components/translate_alert.php');


// Check if the user is logged in
if (!isset($_SESSION['username'])) {
    header("Location: " . url('login.php'));
    exit();
}

$currentUsername = $_SESSION['username'];

// Retrieve user's current username from the database (optional since we have it in session)
// You can remove this part if not needed
$getInfoQuery = "SELECT username FROM users WHERE username = ?";
$stmt = $con->prepare($getInfoQuery);
$stmt->bind_param("s", $currentUsername);
$stmt->execute();
$stmt->bind_result($dbUsername);
$stmt->fetch();
$stmt->close();

// Check if the form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Process form data for updating profile information
    // Validate and sanitize form inputs
    $newUsername = trim($_POST['new_username']);
    $newPassword = trim($_POST['new_password']);

    // Initialize an array to hold update fields
    $updateFields = [];
    $params = [];
    $paramTypes = '';

    // Check if new username is provided and different from the current username
    if (!empty($newUsername) && $newUsername !== $currentUsername) {
        // Check if the new username is already taken
        $checkUsernameQuery = "SELECT username FROM users WHERE username = ?";
        $stmt = $con->prepare($checkUsernameQuery);
        $stmt->bind_param("s", $newUsername);
        $stmt->execute();
        $stmt->store_result();
        $usernameExists = $stmt->num_rows > 0;
        $stmt->close();

        if ($usernameExists) {
            echo "<script>showTranslatedAlert('Username is already taken. Please choose a different username.'); window.location.href = '" . url('pages/edit_security.php') . "';</script>";
            exit();
        } else {
            $updateFields[] = 'username = ?';
            $params[] = $newUsername;
            $paramTypes .= 's';
        }
    } else {
        $newUsername = $currentUsername; // If username is not changed
    }

    // Check if new password is provided
    if (!empty($newPassword)) {
        // Hash the new password
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $updateFields[] = 'password = ?';
        $params[] = $hashedPassword;
        $paramTypes .= 's';
    }

    if (!empty($updateFields)) {
        // Add the current username to the parameters for the WHERE clause
        $params[] = $currentUsername;
        $paramTypes .= 's';

        // Prepare the SQL query
        $updateQuery = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE username = ?";
        $stmt = $con->prepare($updateQuery);
        $stmt->bind_param($paramTypes, ...$params);

        if ($stmt->execute()) {
            // Update session username if username is changed
            if ($_SESSION['username'] !== $newUsername) {
                $_SESSION['username'] = $newUsername;
            }
            // Redirect to the profile page after successful update
            echo "<script>showTranslatedAlert('Profile updated successfully.'); window.location.href = '" . url('dashboard.php') . "';</script>";
            exit();
        } else {
            echo "<script>showTranslatedAlert('Error updating profile. Please try again.');</script>";
        }
        $stmt->close();
    } else {
        echo "<script>showTranslatedAlert('No changes made. Please update at least one field.');</script>";
    }
}

// Set default direction (left to right)
$dir = 'ltr';

// Get the user's language preference
$query = "SELECT app_lang FROM users WHERE username = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("s", $currentUsername);
$stmt->execute();
$stmt->bind_result($app_lang);
$stmt->fetch();
$stmt->close();

// Set direction based on language
if ($app_lang === 'ckb' || $app_lang === 'ar' || $app_lang === 'fa') {
    $dir = 'rtl';
}
?>
<!DOCTYPE html>
<html lang="<?php echo isset($app_lang) ? $app_lang : 'en'; ?>" dir="<?php echo $dir; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C-Health | Edit Security</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://getbootstrap.com/docs/5.3/assets/css/docs.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo url('assets/css/style-dynamic.php'); ?>">
    <link rel="icon" type="image/x-icon" href="<?php echo url('assets/img/fav.ico'); ?>">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <form class="form p-3" action="<?php echo url('pages/edit_security.php'); ?>" method="post">
        <center><img src="<?php echo url('assets/img/logo.png'); ?>" alt="logo" width="100%"></center>
        <h1 class="login-title"><ion-icon name="shield-outline"></ion-icon> Edit Security</h1>
        <div class="my-3">
            <label for="new_username" class="form-label">New Username</label>
            <input type="text" class="form-control p-2 rounded-pill" id="new_username" name="new_username" value="<?php echo htmlspecialchars($currentUsername); ?>">
        </div>
        <div class="mb-3">
            <label for="new_password" class="form-label">New Password</label>
            <input type="password" class="form-control p-2 rounded-pill" id="new_password" name="new_password">
        </div>
        <button type="submit" class="btn btn-success p-2 my-1 rounded-pill cButtonFilling" style="width: 49%;">Update</button>
        <button type="button" onclick="history.back();" class="btn btn-secondary p-2 my-1 rounded-pill cmButton" style="width: 49%;">Cancel</button>
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
    <script src="<?php echo url('assets/js/script.js'); ?>"></script>
</body>
</html>

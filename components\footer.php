<?php
// Include configuration file if not already included
if (!defined('BASE_URL')) {
    require_once __DIR__ . '/../includes/config.php';
}
?>
<!-- Buttons -->
<div class="d-flex mb-2 mx-4">
    <div class="text-center">
        <!-- Button to trigger modal -->
        <a href="#help" class="btn" data-bs-toggle="modal" data-bs-target="#helpModal" style="font-size: 11px !important;">Help</a>
    </div>
    <span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
    <div class="text-center">
        <!-- Button to trigger modal -->
        <a href="#about" class="btn" data-bs-toggle="modal" data-bs-target="#aboutsModal" style="font-size: 11px !important;">About</a>
    </div>
    <span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
    <div class="text-center">
        <!-- Button to trigger modal -->
        <a href="#TermCondition" class="btn" data-bs-toggle="modal" data-bs-target="#termConditionModal" style="font-size: 11px !important;">Term & Condition</a>
    </div>
    <span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
    <div class="text-center">
        <!-- Button to trigger modal -->
        <a href="#PrivacyPolicy" class="btn" data-bs-toggle="modal" data-bs-target="#privacyPolicyModal" style="font-size: 11px !important;">Privacy & Policy</a>
    </div>
</div>

<!-- Modal About -->
<div class="modal modal2 fade pb-5" id="aboutsModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content rounded-0">
            <div class="modal-header px-3">
                <h5 class="modal-title" id="exampleModalLabel">About Us</h5>
            </div>
            <div class="modal-body modal2-body p-3">
                <div class="text-center">
                    <img src="<?php echo url('assets/img/logo.png'); ?>" alt="Logo" height="80px">
                    <span class="d-block">See Your Health!</span>
                </div>
                <div class="container px-3 py-5">
                <h5>Azad Hussein - Founder</h5>
                <p>Public Health Professional</p>
                <a href="mailto:<EMAIL>"><EMAIL></a><br><br>
                <h5>Mohammed Taifur - Development/Design</h5>
                <p>IT Manager</p>
                <a href="mailto:<EMAIL>"><EMAIL></a><br><br>
                <h5>Wada Bahadin - Helper</h5>
                <p>Student</p>
                <a href="mailto:<EMAIL>"><EMAIL></a><br><br>
                <h5>Aryas Abubakr - Helper</h5>
                <p>Student</p>
                <a href="mailto:<EMAIL>"><EMAIL></a><br><br>
                </div>
                <h4>Our Goal</h4>
                <p style="text-indent: 25px;">Our goal is to protects your health by enabling you to monitor your lifestyle daily. Through this app, you can identify any shortcomings in your diet and avoid consuming excessive amounts of unhealthy foods, thereby preventing chronic diseases. Additionally, for individuals with diabetes or high blood pressure, the app allows them to record their test results conveniently. This information is securely stored, aiding doctors in understanding the historical trends of blood sugar and blood pressure levels. By utilizing this app, users can take proactive steps towards managing their health effectively.</p>
            </div>
            <br>
            <hr>
            <div class="px-3 pb-2">
                <h4 class="pt-3">References</h4>
                <span>www.who.int, www.cdc.gov, www.healthline.com, www.fatsecret.com, www.nutritionix.com</span>
            </div>
            <div class="modal-footer d-flex justify-content-center">
                <button type="button" class="btn btn-secondary p-1 m-2 mb-3 px-5 rounded-pill" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Get Started -->
<div class="modal modal2 fade pb-5" id="helpModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content rounded-0">
            <div class="modal-body modal2-body">
                <!-- Getting Started Page 1 -->
                <div id="page1" class="getting-started-page">
                    <img src="<?php echo url('assets/img/slide1.png'); ?>" alt="Slide 1" width="100%">
                    <div class="p-3">
                        <h5 class="fw-bold">Welcome to the C-Health App</h5>
                        <p>In this app, you'll be able to track your daily activities, such as your food intake, exercise routines, and other important health metrics. The app will analyze your data and provide personalized health insights and recommendations.</p>
                    </div>
                </div>
                <!-- Getting Started Page 2 -->
                <div id="page2" class="getting-started-page" style="display:none;">
                    <img src="<?php echo url('assets/img/slide1.png'); ?>" alt="Slide 1" width="100%">
                    <div class="p-3">
                        <h5 class="fw-bold">Tracking Your Health Data</h5>
                        <p>Start by inputting key data such as your age, height, weight, gender, and any relevant medical conditions. You can also track what you eat and your exercise routines. The app uses this information to give you a daily health report.</p>
                    </div>
                </div>
                <!-- Getting Started Page 3 -->
                <div id="page3" class="getting-started-page" style="display:none;">
                    <img src="<?php echo url('assets/img/slide1.png'); ?>" alt="Slide 1" width="100%">
                    <div class="p-3">
                        <h5 class="fw-bold">Get Your Health Insights</h5>
                        <p>Based on your inputs, the app will generate daily reports showing how healthy your lifestyle is. You will also receive recommendations on what to eat, how much exercise to do, and tips to improve your overall health.</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between py-2 my-2">
                <button type="button" class="btn lh-1 rounded-pill p-2 mx-3 px-4 cButton" onclick="showPage(1)" id="backBtn" style="display:none;">Back</button>
                <button type="button" class="btn lh-1 rounded-pill p-2 mx-3 px-4 cButton" onclick="showPage(2)" id="nextBtn">Next</button>
                <button type="button" class="btn lh-1 rounded-pill p-2 mx-3 px-4 cButtonFill" data-bs-dismiss="modal" id="finishBtn" style="display:none;">Get Started</button>
            </div>
        </div>
    </div>
</div>
<script>
    // Function to display the correct page
    function showPage(page) {
        document.querySelectorAll('.getting-started-page').forEach(function(el) {
            el.style.display = 'none';
        });
        document.getElementById('page' + page).style.display = 'block';

        // Update button visibility
        document.getElementById('backBtn').style.display = page === 1 ? 'none' : 'inline-block';
        document.getElementById('nextBtn').style.display = page === 3 ? 'none' : 'inline-block';
        document.getElementById('finishBtn').style.display = page === 3 ? 'inline-block' : 'none';

        // Reset "Next" button click handler depending on the page
        if (page === 1) {
            document.getElementById('nextBtn').setAttribute('onclick', 'showPage(2)');
        } else if (page === 2) {
            document.getElementById('nextBtn').setAttribute('onclick', 'showPage(3)');
        }
    }

    // Reset the modal to start from the first page when closed
    var helpModal = document.getElementById('helpModal');
    helpModal.addEventListener('hidden.bs.modal', function () {
        showPage(1); // Reset to the first page
        // Reset the "Next" button to go to page 2 after modal is closed
        document.getElementById('nextBtn').setAttribute('onclick', 'showPage(2)');
    });
</script>

<?php include(__DIR__ . "/../pages/rules.php"); ?>

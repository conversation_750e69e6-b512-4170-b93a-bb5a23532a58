{"Mohammed": "<PERSON>", "Home": "Home", "Statistics": "Statistics", "Input": "Input", "Guide": "Guide", "Setting": "Setting", "About Us": "About Us", "Healthy": "Healthy", "Non-Healthy": "Non-Healthy", "Kurdish": "Kurdish", "Arabic": "Arabic", "English": "English", "Persian": "Persian", "Turkish": "Turkish", "Kurdî": "Kurd<PERSON>", "male": "male", "female": "female", "kg": "kg", "cm": "cm", "Date:": "Date:", "Time:": "Time:", "Day:": "Day:", "AM": "AM", "PM": "PM", "You  are": "You  are", "You are": "You are", "You did": "You did", "slept": "slept", "hydrated": "hydrated", "exercised": "exercised", "healthy today!": "healthy today!", "Drink water to stay hydrated": "Drink water to stay hydrated", "No health status data available for today, Go to input": "No health status data available for today, Go to input", "Sleep": "Sleep", "at least": "at least", "hours": "hours", "Start exercising to stay healthy": "Start exercising to stay healthy", "User not found.": "User not found.", "Your BMI is": "Your BMI is", ", You are": ", You are", "Nutrition Data for Last Week": "Nutrition Data for Last Week", "Nutrition Data for the Past Week": "Nutrition Data for the Past Week", "Last Week Health": "Last Week Health", "Calories": "Calories", "Carbs": "<PERSON><PERSON>", "Fat": "Fat", "Sugar": "Sugar", "Protein": "<PERSON><PERSON>", "Score": "Score", "Undefined": "Undefined", "Guides": "Guides", "Obese": "Obese", "Obesity": "Obesity", "Hypertension": "Hypertension", "Cardiovascular Disease": "Cardiovascular Disease", "Overweight": "Overweight", "Underweight": "Underweight", "Normal Weight": "Normal Weight", "Date": "Date", "DM (mmol/L)": "DM (mmol/L)", "BP (mmHg)": "BP (mmHg)", "DM": "DM", "BP": "BP", "Close": "Close", "Save as PDF": "Save as PDF", "Nutrition Input Page": "Nutrition Input Page", "You are OVER your ideal weight by": "You are OVER your ideal weight by", "You are UNDER your ideal weight by": "You are UNDER your ideal weight by", "Food": "Food", "Amount": "Amount", "Type": "Type", "Diabetes": "Diabetes", "mmol/L": "mmol/L", "Blood Pressure": "Blood Pressure", "mmHg": "mmHg", "Calculate": "Calculate", "Profile": "Profile", "Username:": "Username:", "Full Name:": "Full Name:", "Gender:": "Gender:", "Age:": "Age:", "Weight:": "Weight:", "Height:": "Height:", "Medical Condition:": "Medical Condition:", "App Language:": "App Language:", "Friends": "Friends", "Friend requests": "Friend requests", "Your Friends": "Your Friends", "Add Friend": "Add Friend", "You have no friends yet.": "You have no friends yet.", "Your Account QR Code": "Your Account QR Code", "QR Code Scanner": "QR Code Scanner", "Edit": "Edit", "Security": "Security", "Logout": "Logout", "Click here to": "Click here to", "again.": "again.", "Invalid or missing token.": "Invalid or missing token.", "The token is invalid or has expired.": "The token is invalid or has expired.", "Error updating password. Please try again.": "Error updating password. Please try again.", "Your password has been successfully reset.": "Your password has been successfully reset.", "Passwords do not match.": "Passwords do not match.", "You must accept the Terms and Conditions and Privacy Policy to register.": "You must accept the Terms and Conditions and Privacy Policy to register.", "Error declining friend request.": "Error declining friend request.", "Error accepting friend request.": "Error accepting friend request.", "Error adding friend.": "Error adding friend.", "Please enter a username.": "Please enter a username.", "Error sending friend request.": "Error sending friend request.", "No user found with that email address.": "No user found with that email address.", "Failed to send recovery email.": "Failed to send recovery email.", "A password recovery email has been sent.": "A password recovery email has been sent.", "No changes made. Please update at least one field.": "No changes made. Please update at least one field.", "Error updating profile. Please try again.": "Error updating profile. Please try again.", "Profile updated successfully.": "Profile updated successfully.", "Not available at this time.": "Not available at this time.", "Username is already taken. Please choose a different username.": "Username is already taken. Please choose a different username.", "You are registered successfully.": "You are registered successfully.", "Required fields are missing or an error occurred.": "Required fields are missing or an error occurred.", "Incorrect Username/password.": "Incorrect Username/password.", "Please enter your current password:": "Please enter your current password:", "Incorrect password. Please try again.": "Incorrect password. Please try again.", "Are you sure you want to log out?": "Are you sure you want to log out?", "EnterPassword": "Please enter your current password:", "IncorrectPassword": "Incorrect password. Please try again.", "ConfirmLogout": "Are you sure you want to log out?", "Azad Hussein - Founder": "<PERSON><PERSON> - Founder", "Public Health Professional": "Public Health Professional", "Mohammed Taifur - Development/Design": "<PERSON> - Development/Design", "IT Manager": "IT Manager", "Wada Bahadin - Helper": "<PERSON><PERSON> - Helper", "Student": "Student", "Aryas Abubakr - Helper": "<PERSON><PERSON><PERSON> - Helper", "Our Goal": "Our Goal", "Our goal is to protects your health by enabling you to monitor your lifestyle daily. Through this app, you can identify any shortcomings in your diet and avoid consuming excessive amounts of unhealthy foods, thereby preventing chronic diseases. Additionally, for individuals with diabetes or high blood pressure, the app allows them to record their test results conveniently. This information is securely stored, aiding doctors in understanding the historical trends of blood sugar and blood pressure levels. By utilizing this app, users can take proactive steps towards managing their health effectively.": "Our goal is to protects your health by enabling you to monitor your lifestyle daily. Through this app, you can identify any shortcomings in your diet and avoid consuming excessive amounts of unhealthy foods, thereby preventing chronic diseases. Additionally, for individuals with diabetes or high blood pressure, the app allows them to record their test results conveniently. This information is securely stored, aiding doctors in understanding the historical trends of blood sugar and blood pressure levels. By utilizing this app, users can take proactive steps towards managing their health effectively.", "References": "References", "Unlock Guide": "Unlock Guide", "Restore Purchase": "Restore Purchase", "Get Premium": "Get Premium", "APP Version": "APP Version", "Lifetime Consumption": "Lifetime Consumption", "Consumption": "Consumption", "Nutrition": "Nutrition", "Quantity": "Quantity", "Count": "Count", "Nutrition Consumption for": "Nutrition Consumption for", "Lifetime Nutrition Consumption": "Lifetime Nutrition Consumption", "milk": "milk", "tea": "tea", "soft drinking": "soft drinking", "water": "water", "zucchini soup": "zucchini soup", "lentil soup": "lentil soup", "chickpea soup": "chickpea soup", "bean soup": "bean soup", "potato soup": "potato soup", "okra soup": "okra soup", "spinach soup": "spinach soup", "apricot soup": "apricot soup", "boiled egg": "boiled egg", "rice": "rice", "bulgur": "bulgur", "barley bread": "barley bread", "wheat bread": "wheat bread", "omlette": "omlette", "cake": "cake", "pizza": "pizza", "burger": "burger", "french fries": "french fries", "biscuit": "biscuit", "dessert": "dessert", "cheese": "cheese", "chicken": "chicken", "meat": "meat", "fish": "fish", "cucumber": "cucumber", "banana": "banana", "apple": "apple", "orange": "orange", "tomato": "tomato", "strawberry": "strawberry", "eggplant": "eggplant", "kiwi": "kiwi", "watermelon": "watermelon", "melon": "melon", "grape": "grape", "peach": "peach", "pear": "pear", "pomegranate": "pomegranate", "pinapple": "pinapple", "lemon": "lemon", "raisins": "raisins", "walnut": "walnut", "dates": "dates", "olive": "olive", "garlic": "garlic", "vegitables salad": "vegitables salad", "yogurt": "yogurt", "fruit salad": "fruit salad", "chips": "chips", "running": "running", "swimming": "swimming", "walking": "walking", "focusing": "focusing", "sleep": "sleep", "cup": "cup", "can": "can", "glass": "glass", "soup bowl": "soup bowl", "piece": "piece", "slice": "slice", "fist": "fist", "dish": "dish", "kurdish size": "kurdish size", "one egg": "one egg", "medium size": "medium size", "single patty": "single patty", "medium serving": "medium serving", "grain": "grain", "small bowl": "small bowl", "bag": "bag", "minute": "minute", "hour": "hour", "Cardiovascular Disease, Diabetes, Hypertension, Obesity": "Cardiovascular Disease, Diabetes, Hypertension, Obesity", "Cardiovascular Disease, Diabetes, Obesity": "Cardiovascular Disease, Diabetes, Obesity", "Cardiovascular Disease, Obesity": "Cardiovascular Disease, Obesity", "Cardiovascular Disease, Diabetes, Hypertension, Overweight": "Cardiovascular Disease, Diabetes, Hypertension, Overweight", "Cardiovascular Disease, Diabetes, Overweight": "Cardiovascular Disease, Diabetes, Overweight", "Cardiovascular Disease, Overweight": "Cardiovascular Disease, Overweight", "Cardiovascular Disease, Diabetes, Hypertension, Underweight": "Cardiovascular Disease, Diabetes, Hypertension, Underweight", "Cardiovascular Disease, Diabetes, Underweight": "Cardiovascular Disease, Diabetes, Underweight", "Cardiovascular Disease, Underweight": "Cardiovascular Disease, Underweight", "Cardiovascular Disease, Diabetes, Hypertension": "Cardiovascular Disease, Diabetes, Hypertension", "Cardiovascular Disease, Diabetes": "Cardiovascular Disease, Diabetes", "Cardiovascular Disease, Hypertension": "Cardiovascular Disease, Hypertension", "Diabetes, Hypertension": "Diabetes, Hypertension", "Diabetes, Obesity": "Diabetes, Obesity", "Diabetes, Overweight": "Diabetes, Overweight", "Diabetes, Underweight": "Diabetes, Underweight", "Diabetes, Hypertension, Obesity": "Diabetes, Hypertension, Obesity", "Hypertension, Obesity": "Hypertension, Obesity", "Diabetes, Hypertension, Overweight": "Diabetes, Hypertension, Overweight", "Hypertension, Overweight": "Hypertension, Overweight", "Diabetes, Hypertension, Underweight": "Diabetes, Hypertension, Underweight", "Hypertension, Underweight": "Hypertension, Underweight", "Edit Profile": "Edit Profile", "Male": "Male", "Female": "Female", "Birthdate:": "Birthdate:", "Weight (kg):": "Weight (kg):", "Height (cm):": "Height (cm):", "Pregnancy": "Pregnancy", "Language:": "Language:", "Update": "Update", "Cancel": "Cancel", "Edit Security": "Edit Security", "New Username": "New Username", "New Password": "New Password", "Registration": "Registration", "Full Name": "Full Name", "Username": "Username", "Password": "Password", "Gender": "Gender", "Birth date": "Birth date", "Weight (kg)": "Weight (kg)", "Height (cm)": "Height (cm)", "Medical Condition": "Medical Condition", "Register": "Register", "I accept the": "I accept the", "and": "and", "Already have an account?": "Already have an account?", "Login here": "Login here", "Login": "<PERSON><PERSON>", "Remember Me": "Remember Me", "Forgot Password?": "Forgot Password?", "Don't have an account?": "Don't have an account?", "Registration Now": "Registration Now", "Forgot Password": "Forgot Password", "Email address": "Email address", "Submit": "Submit", "Reset Password": "Reset Password", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday", "Mon": "Mon", "Tue": "<PERSON><PERSON>", "Wed": "Wed", "Thu": "<PERSON>hu", "Fri": "<PERSON><PERSON>", "Sat": "Sat", "Sun": "Sun", "Help": "Help", "About": "About", "Term & Condition": "Term & Condition", "Terms and Conditions": "Terms and Conditions", "Terms & Conditions": "Terms & Conditions", "Last updated:": "Last updated:", "1. Acceptance of Terms": "1. Acceptance of Terms", "By using C-Health, you agree to be bound by these Terms and Conditions, as well as any additional terms or policies that may apply. If you do not agree to these terms, you may not access or use the service.": "By using C-Health, you agree to be bound by these Terms and Conditions, as well as any additional terms or policies that may apply. If you do not agree to these terms, you may not access or use the service.", "2. Description of Service": "2. Description of Service", "C-Health is a Progressive Web Application (PWA) designed to help users track their daily activities, provide personalized recommendations for health improvement, and analyze nutrition and physical activities using machine learning algorithms. The app aims to enhance user well-being by offering guidance for healthier lifestyle choices based on user-input data.": "C-Health is a Progressive Web Application (PWA) designed to help users track their daily activities, provide personalized recommendations for health improvement, and analyze nutrition and physical activities using machine learning algorithms. The app aims to enhance user well-being by offering guidance for healthier lifestyle choices based on user-input data.", "3. User Responsibilities": "3. User Responsibilities", "You are responsible for maintaining the confidentiality of your account login credentials and are fully responsible for all activities that occur under your account.": "You are responsible for maintaining the confidentiality of your account login credentials and are fully responsible for all activities that occur under your account.", "You must provide accurate and complete information when registering and using C-Health. You are responsible for updating any information that may change over time.": "You must provide accurate and complete information when registering and using C-Health. You are responsible for updating any information that may change over time.", "The app is not intended to diagnose, treat, or cure any medical conditions. It provides health recommendations based on data entered by users, but it is essential to consult with a healthcare professional before making significant health or lifestyle changes.": "The app is not intended to diagnose, treat, or cure any medical conditions. It provides health recommendations based on data entered by users, but it is essential to consult with a healthcare professional before making significant health or lifestyle changes.", "You agree to comply with all applicable laws and regulations while using C-Health.": "You agree to comply with all applicable laws and regulations while using C-Health.", "4. Use of Data": "4. Use of Data", "C-Health collects personal data such as weight, height, age, medical conditions, and daily activity details for the purpose of generating personalized health insights.": "C-Health collects personal data such as weight, height, age, medical conditions, and daily activity details for the purpose of generating personalized health insights.", "Your data will only be used to provide personalized health reports and improve our service. It will not be shared with third parties without your explicit consent.": "Your data will only be used to provide personalized health reports and improve our service. It will not be shared with third parties without your explicit consent.", "We implement industry-standard security measures to protect your data. However, we cannot guarantee absolute security, and you use the service at your own risk.": "We implement industry-standard security measures to protect your data. However, we cannot guarantee absolute security, and you use the service at your own risk.", "You consent to the collection and processing of your personal data by using the app. For more details, please review our": "You consent to the collection and processing of your personal data by using the app. For more details, please review our", "Privacy Policy": "Privacy Policy", "5. Prohibited Activities": "5. Prohibited Activities", "You agree not to:": "You agree not to:", "- Use the app for any illegal or unauthorized purpose.": "- Use the app for any illegal or unauthorized purpose.", "- Attempt to reverse-engineer, modify, or tamper with any part of the app.": "- Attempt to reverse-engineer, modify, or tamper with any part of the app.", "- Upload or share any harmful, malicious, or inappropriate content that could disrupt the app's functionality or violate the rights of others.": "- Upload or share any harmful, malicious, or inappropriate content that could disrupt the app's functionality or violate the rights of others.", "6. Intellectual Property": "6. Intellectual Property", "The design, interface, logo, and content of C-Health are protected by intellectual property laws. Unauthorized use, copying, or distribution of any materials from C-Health without explicit permission may result in legal action.": "The design, interface, logo, and content of C-Health are protected by intellectual property laws. Unauthorized use, copying, or distribution of any materials from C-Health without explicit permission may result in legal action.", "7. Medical Disclaimer": "7. Medical Disclaimer", "C-Health is intended to be a supplementary health-tracking tool. The insights and recommendations provided are based on user-entered data and general health guidelines. It does not replace professional medical advice. Always consult with a healthcare professional for personalized medical guidance.": "C-Health is intended to be a supplementary health-tracking tool. The insights and recommendations provided are based on user-entered data and general health guidelines. It does not replace professional medical advice. Always consult with a healthcare professional for personalized medical guidance.", "8. Disclaimer of Warranties": "8. <PERSON><PERSON><PERSON> of Warranties", "C-Health is provided on an \"as-is\" and \"as-available\" basis. While we strive to provide accurate and helpful health insights, we make no warranties regarding the accuracy, reliability, or completeness of the data or recommendations provided. Use of the app is at your own risk.": "C-Health is provided on an \"as-is\" and \"as-available\" basis. While we strive to provide accurate and helpful health insights, we make no warranties regarding the accuracy, reliability, or completeness of the data or recommendations provided. Use of the app is at your own risk.", "9. Limitation of Liability": "9. Limitation of Liability", "C-Health and its developers will not be held liable for any damages, including indirect, incidental, or consequential damages, arising out of your use of the app or reliance on any data provided. You acknowledge that health and lifestyle decisions should be made in consultation with qualified professionals.": "C-Health and its developers will not be held liable for any damages, including indirect, incidental, or consequential damages, arising out of your use of the app or reliance on any data provided. You acknowledge that health and lifestyle decisions should be made in consultation with qualified professionals.", "10. Changes to the Terms": "10. Changes to the Terms", "We reserve the right to update these Terms and Conditions from time to time to reflect changes in our services, technology, or legal obligations. Any updates will be posted on this page, and it is your responsibility to review the latest version. Your continued use of C-Health following any updates constitutes your acceptance of the new terms.": "We reserve the right to update these Terms and Conditions from time to time to reflect changes in our services, technology, or legal obligations. Any updates will be posted on this page, and it is your responsibility to review the latest version. Your continued use of C-Health following any updates constitutes your acceptance of the new terms.", "11. Governing Law": "11. Governing Law", "These Terms and Conditions are governed by and construed in accordance with the laws of the Republic of Iraq, specifically the Kurdistan Region. Any disputes arising from these terms or your use of the service will be resolved under the jurisdiction of the courts in the Kurdistan Region of Iraq.": "These Terms and Conditions are governed by and construed in accordance with the laws of the Republic of Iraq, specifically the Kurdistan Region. Any disputes arising from these terms or your use of the service will be resolved under the jurisdiction of the courts in the Kurdistan Region of Iraq.", "12. Contact Information": "12. Contact Information", "If you have any questions or concerns about these Terms and Conditions, or if you would like to request additional information, please contact us at": "If you have any questions or concerns about these Terms and Conditions, or if you would like to request additional information, please contact us at", "Privacy & Policy": "Privacy & Policy", "Privacy & Policies": "Privacy & Policies", "Privacy and Policies": "Privacy and Policies", "1. Introduction": "1. Introduction", "At C-Health, we value your privacy. This Privacy Policy outlines how we collect, use, disclose, and safeguard your information when you use our web application. By using C-Health, you agree to the collection and use of information in accordance with this policy.": "At C-Health, we value your privacy. This Privacy Policy outlines how we collect, use, disclose, and safeguard your information when you use our web application. By using C-Health, you agree to the collection and use of information in accordance with this policy.", "2. Information We Collect": "2. Information We Collect", "Personal Data:": "Personal Data:", "When you register and use our app, we may collect personally identifiable information, including but not limited to your name, email, date of birth, weight, height, gender, medical conditions, and activity data.": "When you register and use our app, we may collect personally identifiable information, including but not limited to your name, email, date of birth, weight, height, gender, medical conditions, and activity data.", "Non-Personal Data:": "Non-Personal Data:", "We may also collect non-personal information such as device information, browser type, and usage data for improving the user experience.": "We may also collect non-personal information such as device information, browser type, and usage data for improving the user experience.", "3. How We Use Your Information": "3. How We Use Your Information", "The information we collect helps us:": "The information we collect helps us:", "Generate personalized health reports and insights.": "Generate personalized health reports and insights.", "Provide recommendations for nutrition and physical activities.": "Provide recommendations for nutrition and physical activities.", "Improve our services and offer tailored advice based on your progress.": "Improve our services and offer tailored advice based on your progress.", "Send you periodic updates and reminders if you opt-in.": "Send you periodic updates and reminders if you opt-in.", "4. Data Sharing": "4. Data Sharing", "We do not share your personal data with third parties without your explicit consent, except in the following cases:": "We do not share your personal data with third parties without your explicit consent, except in the following cases:", "Compliance with laws:": "Compliance with laws:", "If we are required by law to share your data.": "If we are required by law to share your data.", "Service Providers:": "Service Providers:", "We may share your data with trusted third-party service providers to improve the app’s functionality.": "We may share your data with trusted third-party service providers to improve the app’s functionality.", "5. Data Security": "5. Data Security", "We use industry-standard security measures to protect your data. However, no security system is foolproof. You use the service at your own risk, and we cannot guarantee the absolute security of your information.": "We use industry-standard security measures to protect your data. However, no security system is foolproof. You use the service at your own risk, and we cannot guarantee the absolute security of your information.", "6. Your Rights": "6. Your Rights", "You have the right to access, correct, or delete your personal information at any time. You can do so through your account settings or by contacting us at": "You have the right to access, correct, or delete your personal information at any time. You can do so through your account settings or by contacting us at", "7. Data Retention": "7. Data Retention", "We retain your data for as long as necessary to provide you with the service or as required by law. You can request the deletion of your data, and we will honor such requests unless legal obligations require us to keep it.": "We retain your data for as long as necessary to provide you with the service or as required by law. You can request the deletion of your data, and we will honor such requests unless legal obligations require us to keep it.", "8. Cookies and Tracking Technologies": "8. Cookies and Tracking Technologies", "C-Health uses cookies to enhance your experience, analyze app usage, and provide tailored content. A cookie is a small file placed on your device to remember your preferences. We use different types of cookies:": "C-Health uses cookies to enhance your experience, analyze app usage, and provide tailored content. A cookie is a small file placed on your device to remember your preferences. We use different types of cookies:", "Essential Cookies:": "Essential Cookies:", "These are necessary for the app to function properly.": "These are necessary for the app to function properly.", "Performance Cookies:": "Performance Cookies:", "These help us understand how users interact with the app so we can improve its performance.": "These help us understand how users interact with the app so we can improve its performance.", "Functional Cookies:": "Functional Cookies:", "These cookies remember your settings and preferences to provide a more personalized experience.": "These cookies remember your settings and preferences to provide a more personalized experience.", "You can control cookies through your browser settings. Disabling cookies may affect your experience with the app.": "You can control cookies through your browser settings. Disabling cookies may affect your experience with the app.", "9. Changes to this Privacy Policy": "9. Changes to this Privacy Policy", "We may update this Privacy Policy from time to time. We will notify you of any changes by updating the \"Last Updated\" date at the top of this policy. Continued use of the app after changes are made constitutes your acceptance of the new Privacy Policy.": "We may update this Privacy Policy from time to time. We will notify you of any changes by updating the \"Last Updated\" date at the top of this policy. Continued use of the app after changes are made constitutes your acceptance of the new Privacy Policy.", "10. Governing Law": "10. Governing Law", "This Privacy Policy is governed by and construed in accordance with the laws of the Republic of Iraq, specifically the Kurdistan Region.": "This Privacy Policy is governed by and construed in accordance with the laws of the Republic of Iraq, specifically the Kurdistan Region.", "11. Contact Us": "11. Contact Us", "If you have any questions about this Privacy Policy, please contact us at": "If you have any questions about this Privacy Policy, please contact us at", "See Your Health!": "See Your Health!", "Next": "Next", "Back": "Back", "Get Started": "Get Started", "Welcome to the C-Health App": "Welcome to the C-Health App", "In this app, you'll be able to track your daily activities, such as your food intake, exercise routines, and other important health metrics. The app will analyze your data and provide personalized health insights and recommendations.": "In this app, you'll be able to track your daily activities, such as your food intake, exercise routines, and other important health metrics. The app will analyze your data and provide personalized health insights and recommendations.", "Tracking Your Health Data": "Tracking Your Health Data", "Start by inputting key data such as your age, height, weight, gender, and any relevant medical conditions. You can also track what you eat and your exercise routines. The app uses this information to give you a daily health report.": "Start by inputting key data such as your age, height, weight, gender, and any relevant medical conditions. You can also track what you eat and your exercise routines. The app uses this information to give you a daily health report.", "Get Your Health Insights": "Get Your Health Insights", "Based on your inputs, the app will generate daily reports showing how healthy your lifestyle is. You will also receive recommendations on what to eat, how much exercise to do, and tips to improve your overall health.": "Based on your inputs, the app will generate daily reports showing how healthy your lifestyle is. You will also receive recommendations on what to eat, how much exercise to do, and tips to improve your overall health."}
<?php
// Include configuration file
require_once __DIR__ . '/../../includes/config.php';

// Set the content type to CSS
header('Content-Type: text/css');
?>
/* Font declarations with dynamic paths */
@font-face {
font-family: "c";
src: url("<?php echo BASE_URL; ?>/assets/fonts/c.ttf");
}
@font-face {
font-family: "cb";
src: url("<?php echo BASE_URL; ?>/assets/fonts/cb.ttf");
}
@font-face {
font-family: "ci";
src: url("<?php echo BASE_URL; ?>/assets/fonts/ci.ttf");
}
@font-face {
font-family: "cbi";
src: url("<?php echo BASE_URL; ?>/assets/fonts/cbi.ttf");
}
@font-face {
font-family: "ck";
src: url("<?php echo BASE_URL; ?>/assets/fonts/ck.ttf");
}
.c {
font-family: "c";
}

.cb {
font-family: "cb";
}

.ci {
font-family: "ci";
}

.cbi {
font-family: "cbi";
}

.ck {
font-family: "ck";
}
* {
margin: 0 !important;
padding: 0 !important;
box-sizing: border-box !important;
font-family: "ck" !important;
}
.cButton {
font-weight: bolder !important;
text-decoration: none;
border: 3px solid #2e7d32 !important;
color: #2e7d32;
background-color: transparent !important;
}
.cButtonFill {
font-weight: bold !important;
text-decoration: none;
border: 3px solid #2e7d32 !important;
color: #ffeb3b !important;
background-color: #2e7d32 !important;
}
.cButtonFilling {
font-weight: bold !important;
text-decoration: none;
border: 3px solid #2e7d32 !important;
color: white !important;
background-color: #2e7d32 !important;
}
.cButton:hover {
font-weight: bolder !important;
text-decoration: none;
border: 3px solid #2e7d32 !important;
color: white !important;
background-color: #2e7d32 !important;
}
.cmButton {
font-weight: bolder !important;
text-decoration: none;
border: 3px solid #5c636a !important;
color: #5c636a !important;
background-color: transparent !important;
}
.cmButton:hover {
font-weight: bolder !important;
text-decoration: none;
border: 3px solid #5c636a !important;
color: white !important;
background-color: #5c636a !important;
}
.text-green {
color: #2e7d32 !important;
}
.bg-green {
background-color: #2e7d32 !important;
}
.text-yellow {
color: #ffeb3b !important;
}
.bg-yellow {
background-color: #ffeb3b !important;
}
.text-gray {
color: #666666;
}

/* Change checkbox background and border colors */
.custom-checkbox {
width: 20px;
height: 20px;
border-radius: 5px;
border: 2px solid #2e7d32; /* Deep green border */
background-color: white;
transition: all 0.3s ease;
}

/* Change color when checked */
.custom-checkbox:checked {
background-color: #2e7d32; /* Deep green when checked */
border-color: #2e7d32;
}
.custom-checkbox {
border-radius: 50px !important;
width: 20px !important;
height: 20px !important;
}

/* Select all checkbox inputs */
input[type="checkbox"] {
/* Your styles here */
accent-color: #2e7d32; /* Example to change checkbox accent color */
width: 12px;
height: 12px;
}

/* Select checkbox when it is checked */
input[type="checkbox"]:checked {
/* Your styles for a checked checkbox */
background-color: #2e7d32; /* Example to change background color */
border-color: #2e7d32;
}

.form-control:focus {
outline: none !important;
border-color: #2e7d32 !important; /* Deep green border */
box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.25) !important; /* Deep green shadow */
}
input:focus,
textarea:focus,
select:focus {
outline: none !important; /* Removes default outline */
border-color: #2e7d32 !important; /* Changes border color to deep green */
border-radius: 50px !important;
box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.25) !important; /* Adds a green shadow */
}

.acceptCheckbox input {
margin-top: 15px !important;
}

#lang-select {
border-radius: 50px !important;
}

.upperTab {
width: 100%;
height: 70px !important;
position: fixed;
top: 0;
background-color: #e0e0e0;
color: #e0e0e0;
}
.alogo .logo {
height: 60px !important;
display: flex;
justify-content: start !important;
margin: 0 auto !important;
padding: 0 !important;
}
body {
display: flex;
justify-content: center;
align-items: center;
min-height: auto;
background: #ffffff !important;
}
.navigation {
position: fixed;
bottom: 0;
width: 100%;
height: 70px;
background: #e0e0e0;
display: flex;
justify-content: center;
align-items: center;
z-index: 1000 !important;
}
.navigation ul {
display: flex;
width: 350px;
}
.navigation ul li {
position: relative;
width: 70px;
height: 70px;
list-style: none;
z-index: 1;
}
.navigation ul li a {
position: relative;
display: flex;
justify-content: center;
align-items: center;
flex-direction: column;
width: 100%;
text-align: center;
font-weight: 500;
}
.navigation ul li a .icon {
position: relative;
display: block;
line-height: 75px;
font-size: 1.5em;
text-align: center;
color: #fff;
transition: 0.5s;
}
.navigation ul li.active a .icon {
transform: translateY(-37px);
}
.navigation ul li.active {
transform: translateY(9px);
}
.navigation ul li a .text {
position: absolute;
color: #fff;
font-weight: 400;
font-size: 0.75em;
letter-spacing: 0.05em;
opacity: 0;
transform: translateY(20px);
transition: 0.5s;
}
.navigation ul li.active a .text {
opacity: 1;
transform: translateY(15px);
}
.navigation ul li.active a .icon {
background-color: #e0e0e0;
width: 58px;
height: 58px;
border-radius: 50%;
bottom: 0;
top: 0;
}
.navigation ul li a .icon {
background-color: transparent;
width: 58px;
height: 58px;
border-radius: 50%;
bottom: 0;
top: 0;
}
.navigation ul li.active .navIcons {
transform: translateY(-3px);
}
.navigation ul li a .circle {
position: absolute;
display: block;
width: 50px;
height: 50px;
background: transparent;
border-radius: 50%;
border: 1.8px solid #2e7d32;
transform: translateY(-37px) scale(0);
}
.navigation ul li.active a .circle {
transition: 0.5s;
transition-delay: 0.5s;
transform: translateY(-37px) scale(1);
}
.indicator {
position: absolute;
top: -50%;
width: 70px;
height: 70px;
background: #ffffff !important;
border: 6px solid #ffffff;
border-radius: 50%;
display: flex;
justify-content: center;
align-items: center;
transition: 0.5s;
clip-path: ellipse(
300% 51% at 50% 100%
); /* Clip top half to make it transparent */
}
.indicator::before {
content: "";
position: absolute;
top: 50%;
left: -22px;
width: 20px;
height: 20px;
background: transparent;
border-top-right-radius: 20px;
box-shadow: 1px -10px 0 #ffffff;
}
.indicator::after {
content: "";
position: absolute;
top: 50%;
right: -22px;
width: 20px;
height: 20px;
background: transparent;
border-top-left-radius: 20px;
box-shadow: -1px -10px 0 #ffffff;
}
.navigation ul li:nth-child(1).active ~ .indicator {
transform: translateX(calc(70px * 0));
}
.navigation ul li:nth-child(2).active ~ .indicator {
transform: translateX(calc(70px * 1));
}
.navigation ul li:nth-child(3).active ~ .indicator {
transform: translateX(calc(70px * 2));
}
.navigation ul li:nth-child(4).active ~ .indicator {
transform: translateX(calc(70px * 3));
}
.navigation ul li:nth-child(5).active ~ .indicator {
transform: translateX(calc(70px * 4));
}

[dir="rtl"] .navigation ul li:nth-child(1).active ~ .indicator {
transform: translateX(calc(-70px * 0));
}
[dir="rtl"] .navigation ul li:nth-child(2).active ~ .indicator {
transform: translateX(calc(-70px * 1));
}
[dir="rtl"] .navigation ul li:nth-child(3).active ~ .indicator {
transform: translateX(calc(-70px * 2));
}
[dir="rtl"] .navigation ul li:nth-child(4).active ~ .indicator {
transform: translateX(calc(-70px * 3));
}
[dir="rtl"] .navigation ul li:nth-child(5).active ~ .indicator {
transform: translateX(calc(-70px * 4));
}

.icon,
.text,
.circle {
color: #2e7d32 !important;
border-color: #2e7d32 !important;
}
.upperTab,
.navigation {
z-index: 10;
}

/* Your initial styles here */
.form {
margin: 50px auto;
width: 300px;
padding: 30px 25px;
background: white;
}
h1.login-title {
color: #666;
margin: 0px auto 25px;
font-size: 25px;
font-weight: 300;
text-align: center;
}
.login-input {
font-size: 15px;
border: 1px solid #ccc !important;
padding: 20px;
margin-bottom: 25px;
height: 25px;
width: calc(100% - 23px);
}
.login-input:focus {
border-color: #6e8095;
outline: none;
}
.login-button {
color: #fff;
background: #55a1ff;
border: 0;
outline: 0;
width: 100%;
height: 50px;
font-size: 16px;
text-align: center;
cursor: pointer;
}
.link {
color: #666;
font-size: 15px;
text-align: center;
margin-bottom: 0px;
}
.link a {
color: #666;
}
h3 {
font-weight: normal;
text-align: center;
}
.pages {
width: 100% !important;
height: auto;
position: absolute !important;
padding: 70px 0 !important;
}
.centered {
display: flex !important;
justify-content: center !important;
align-items: center !important;
}
.centerH {
display: flex;
justify-content: center;
}
.centerV {
display: flex;
align-items: center;
}
.supportLower {
background: rgb(255, 255, 255);
background: -moz-linear-gradient(
180deg,
rgba(255, 255, 255, 0) 0%,
rgba(255, 255, 255, 1) 26%,
rgba(255, 255, 255, 1) 100%
);
background: -webkit-linear-gradient(
180deg,
rgba(255, 255, 255, 0) 0%,
rgba(255, 255, 255, 1) 26%,
rgba(255, 255, 255, 1) 100%
);
background: linear-gradient(
180deg,
rgba(255, 255, 255, 0) 0%,
rgba(255, 255, 255, 1) 26%,
rgba(255, 255, 255, 1) 100%
);
filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#ffffff",endColorstr="#ffffff",GradientType=1);
height: 100px;
width: 100%;
z-index: 10;
position: fixed;
bottom: 0;
}

#statistics,
#profile,
#input,
#guide {
z-index: 5;
display: none;
top: 0 !important;
height: 100vh !important;
padding: 80px 0 !important;
}
#home {
z-index: 6 !important;
display: block;
top: 0 !important;
height: 100vh !important;
padding: 80px 0 !important;
}

.form-check .form-check-input {
float: none !important;
}

.modal {
z-index: 1000 !important;
top: 70px;
}
.modal2 {
top: 70px;
bottom: 80px;
padding-bottom: 150px !important;
}
.modal-backdrop {
z-index: 0 !important;
}
.modal-body {
height: 50vh !important;
}
.modal2-body {
height: auto !important;
}
#lifetimeModal, #dailyModal, .dailyConMod {
padding-bottom: 200px !important;
}

select {
padding: 5px;
border: 1px solid #ccc;
border-radius: 4px;
direction: ltr; /* Ensure text direction is left-to-right by default */
}
/* Adjust dropdown arrow position for right-to-left languages */
select:lang(ar) {
padding-right: 25px; /* Adjust padding to accommodate the arrow */
}
select:lang(ar) option {
direction: rtl; /* Ensure option text direction is right-to-left */
}
/* Use unicode character for left arrow */
select:lang(ar)::after {
content: "\25c0"; /* Unicode character for left arrow */
position: absolute;
top: 50%;
right: 8px;
transform: translateY(-50%);
}

/* For Webkit/Blink Browsers (e.g., Chrome, Safari) */
::-webkit-selection {
background-color: #2e7d32; /* Desired color */
color: #fff; /* Text color */
}

/* For Mozilla Firefox */
::selection {
background-color: #2e7d32; /* Desired color */
color: #fff; /* Text color */
}

.navIcons {
width: 25px;
padding-bottom: 15px !important;
}

.bgb {
background-image: url("<?php echo BASE_URL; ?>/assets/img/bg.jpg") !important;
height: 100%;
overflow: scroll;
}

input[type="date"]::before {
content: attr(placeholder);
position: absolute;
color: #595c5f;
}

input[type="date"] {
color: #ffffff;
}

input[type="date"]:focus,
input[type="date"]:valid {
color: #212529;
}
input[type="date"]:focus::before,
input[type="date"]:valid::before {
content: "";
}
/* Default color for select */
.select-default {
color: #595c5f;
}

/* Changed color for select */
.select-male, .select-female{
color: #212529;
}

.indicator {
position: absolute;
}
.indicatorB {
position: relative;
width: 40;
border-top: 1px solid transparent !important;
border-right: 2px solid transparent !important;
border-left: 2px solid transparent !important;
background: -moz-linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 60%, rgba(224,224,224,1) 100%);
background: -webkit-linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 60%, rgba(224,224,224,1) 100%);
background: linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 60%, rgba(224,224,224,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#ffffff",endColorstr="#ffffff",GradientType=1);
}
* {
-webkit-user-select: none !important; /* Safari */
-ms-user-select: none !important; /* IE 10 and IE 11 */
user-select: none !important; /* Standard syntax */
user-drag: none !important;
-webkit-user-drag: none !important;
-moz-user-select: none !important;
}

#aboutsModal, #helpModal, #privacyPolicyModal, #termConditionModal {
top: 0 !important;
bottom: 0 !important;
padding-bottom: 0 !important;
}

.modal-body p {
text-indent: 25px !important;
}
.modal-body h4 {
margin-top: 1rem !important;
}
.modal-body li {
margin: 0 1rem !important;
}

/* Water Circle Animation */
.waterCircle {
position: relative;
overflow: hidden;
}

.bubbles-container {
position: absolute;
bottom: 0;
left: 50%;
width: 100%;
height: 100%;
transform: translateX(-50%);
pointer-events: none; /* Prevent interaction */
}

.bubble {
position: absolute;
bottom: 0;
width: 10px;
height: 10px;
background-color: #2196F3; /* Water-like color */
border-radius: 50%;
opacity: 0.8;
animation: floatUp 4s ease-in-out infinite;
}

/* Vary the sizes of the bubbles */
.bubble:nth-child(1) {
left: 10%;
width: 12px;
height: 12px;
animation-delay: 0s;
animation-duration: 5s;
}

.bubble:nth-child(2) {
left: 30%;
width: 8px;
height: 8px;
animation-delay: 1s;
animation-duration: 6s;
}

.bubble:nth-child(3) {
left: 50%;
width: 15px;
height: 15px;
animation-delay: 0.5s;
animation-duration: 4.5s;
}

.bubble:nth-child(4) {
left: 70%;
width: 10px;
height: 10px;
animation-delay: 0.8s;
animation-duration: 5.2s;
}

.bubble:nth-child(5) {
left: 90%;
width: 7px;
height: 7px;
animation-delay: 1.2s;
animation-duration: 6.5s;
}

/* Keyframes for smooth floating upwards */
@keyframes floatUp {
0% {
transform: translateY(0);
opacity: 0.8;
}
50% {
opacity: 1;
}
100% {
transform: translateY(-120%);
opacity: 0;
}
}

/* Exercise Circle Animation */
.exerciseCircle {
position: relative;
overflow: hidden;
}

.pulsing-dots-container {
position: absolute;
bottom: 10%;
left: 50%;
transform: translateX(-50%);
display: flex;
gap: 10px;
}

.pulse-dot {
width: 10px;
height: 10px;
background-color: #FF9800;
border-radius: 50%;
animation: randomPulseAndMove 4s infinite ease-in-out;
opacity: 0.7;
}

/* Each dot will have slightly different timing */
.pulse-dot:nth-child(1) {
animation-duration: 3.5s;
animation-delay: 0.2s;
}

.pulse-dot:nth-child(2) {
animation-duration: 4.2s;
animation-delay: 0.6s;
}

.pulse-dot:nth-child(3) {
animation-duration: 5s;
animation-delay: 0.4s;
}

/* Keyframes for random pulse and subtle movement */
@keyframes randomPulseAndMove {
0% {
transform: translate(0px, 0px) scale(1);
opacity: 0.8;
}
25% {
transform: translate(2px, -2px) scale(1.2);
opacity: 1;
}
50% {
transform: translate(-2px, 2px) scale(0.9);
opacity: 0.7;
}
75% {
transform: translate(1px, -1px) scale(1.3);
opacity: 0.9;
}
100% {
transform: translate(0px, 0px) scale(1);
opacity: 0.8;
}
}

/* Sleep Circle Animation */
.sleepCircle {
position: relative;
overflow: hidden;
}

.zzz-container {
position: absolute;
bottom: 10px;
left: 50%;
transform: translateX(-50%);
}

.zzz {
display: block;
font-size: 14px;
color: #673AB7;
position: absolute;
opacity: 0;
animation: floatUp 3s ease-in-out infinite;
}

.zzz:nth-child(1) {
left: -20px;
animation-delay: 0s;
}

.zzz:nth-child(2) {
left: 0px;
animation-delay: 1s;
}

.zzz:nth-child(3) {
left: 20px;
animation-delay: 2s;
}

/* Language and Direction Specific Styles */
html[dir="ltr"] #gender {
direction: ltr;
text-align: left;
}
html[dir="rtl"] #gender {
padding-right: 1.75rem !important;
direction: rtl;
text-align: right;
}

#friendListModal .modal-body {
height: 40vh !important;
overflow-y: scroll !important;
direction: ltr;
text-align: start;
}

#language-selection {
width: 125px;
direction: rtl !important;
right: -40px;
}
#language-selection i {
color: #343a40;
margin-left: -50px !important;
z-index: -5;
}
#language-selection select{
color: #343a40;
margin-right: -60px !important;
text-align: left;
}
html[lang="kmr"] #language-selection {
width: 135px !important;
}
html[lang="en"] #language-selection, html[lang="tr"] #language-selection {
width: 118px !important;
}
html[lang="ckb"] #language-selection, html[lang="ar"] #language-selection {
width: 108px !important;
}
html[lang="fa"] #language-selection {
width: 105px !important;
}
.getting-started-page img {
margin-top: -100px !important;
}

#dailyModal,
.dailyConMod {
z-index: 1050 !important;
}